#!/usr/bin/env python3
"""
Test script to verify the AsymmetricTradingEnv AttributeError fix.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from trading.asymmetric_env import AsymmetricTradingEnv, AsymmetricConfig
    print("✓ Successfully imported AsymmetricTradingEnv")
except ImportError as e:
    print(f"✗ Failed to import AsymmetricTradingEnv: {e}")
    sys.exit(1)

def create_test_data():
    """Create minimal test data for environment initialization."""
    # Create a simple dataset with required columns
    dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
    
    data = []
    for date in dates:
        data.append({
            'date': date,
            'tic': 'AAPL',
            'close': 150.0 + np.random.randn() * 5,  # Random price around 150
            'open': 149.0 + np.random.randn() * 5,
            'high': 152.0 + np.random.randn() * 5,
            'low': 148.0 + np.random.randn() * 5,
            'volume': 1000000,
            'macd': np.random.randn() * 0.1,
            'rsi': 50 + np.random.randn() * 10,
            'cci': np.random.randn() * 20,
            'adx': 25 + np.random.randn() * 5,
        })
    
    df = pd.DataFrame(data)
    df = df.set_index('date')
    return df

def test_asymmetric_env_initialization():
    """Test AsymmetricTradingEnv initialization."""
    print("\n=== Testing AsymmetricTradingEnv Initialization ===")
    
    try:
        # Create test data
        df = create_test_data()
        print(f"✓ Created test data with shape: {df.shape}")
        print(f"  Columns: {list(df.columns)}")
        print(f"  Date range: {df.index.min()} to {df.index.max()}")
        
        # Create asymmetric config
        config = AsymmetricConfig(
            asymmetric_ratio=2.0,
            volatility_lookback=5,
            momentum_threshold=0.02,
            volatility_threshold=0.1
        )
        print("✓ Created AsymmetricConfig")
        
        # Test environment initialization
        print("\n--- Attempting to initialize AsymmetricTradingEnv ---")
        
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=1,
            hmax=100,
            initial_amount=10000,
            num_stock_shares=[0],
            buy_cost_pct=0.001,
            sell_cost_pct=0.001,
            reward_scaling=1e-4,
            state_space=0,  # Will be calculated
            action_space=1,
            tech_indicator_list=['macd', 'rsi', 'cci', 'adx'],
            asymmetric_config=config,
            log_level="INFO"
        )
        
        print("✓ Successfully initialized AsymmetricTradingEnv!")
        print(f"  State space: {env.observation_space.shape}")
        print(f"  Action space: {env.action_space.shape}")
        
        # Test reset
        print("\n--- Testing environment reset ---")
        obs = env.reset()
        print(f"✓ Successfully reset environment!")
        print(f"  Observation shape: {obs.shape if hasattr(obs, 'shape') else 'N/A'}")
        print(f"  Observation type: {type(obs)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to initialize AsymmetricTradingEnv: {e}")
        import traceback
        print(f"Traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("Testing AsymmetricTradingEnv AttributeError fix...")
    
    success = test_asymmetric_env_initialization()
    
    if success:
        print("\n🎉 All tests passed! The AttributeError fix appears to be working.")
    else:
        print("\n❌ Tests failed. The fix may need further adjustments.")
        sys.exit(1)
