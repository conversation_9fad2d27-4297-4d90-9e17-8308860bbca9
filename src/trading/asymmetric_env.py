"""Asymmetric Trading Environment for FinRL.

This module implements a custom trading environment that targets asymmetric return profiles,
where the strategy aims to capture more upside than downside risk.

Key Features:
- Asymmetric reward function favoring upside returns
- Dynamic position sizing based on market conditions
- Risk-adjusted action scaling
- Enhanced state representation with asymmetry indicators
- Integration with FinRL framework
"""

import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Optional, Tuple, Any
import warnings
from datetime import datetime
import traceback

try:
    from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
except ImportError:
    # Fallback if FinRL is not available
    class StockTradingEnv:
        def __init__(self, *args, **kwargs):
            raise ImportError("FinRL not available. Please install FinRL.")

# Import settings for centralized configuration
try:
    from config.settings import Settings
    settings = Settings()
except ImportError:
    # Fallback if settings not available
    settings = None

from utils.logging import get_logger
from strategies.asymmetric_strategy import AsymmetricConfig


class AsymmetricTradingEnv(StockTradingEnv):
    """Asymmetric trading environment extending FinRL's StockTradingEnv.
    
    This environment implements an asymmetric return profile strategy that:
    1. Rewards upside movements more than it penalizes downside movements
    2. Adjusts position sizing based on market volatility and momentum
    3. Incorporates risk-adjusted reward scaling
    4. Provides enhanced state representation for asymmetric decision making
    """
    
    def __init__(
        self,
        df: pd.DataFrame,
        stock_dim: int,
        hmax: int,
        initial_amount: int,
        num_stock_shares: List[int],
        buy_cost_pct: List[float],
        sell_cost_pct: List[float],
        reward_scaling: float = 1e-4,
        state_space: int = None,
        action_space: int = None,
        tech_indicator_list: List[str] = None,
        turbulence_threshold: Optional[float] = None,
        risk_indicator_col: str = "turbulence",
        make_plots: bool = False,
        print_verbosity: int = 10,
        day: int = 0,
        initial: bool = True,
        previous_state: List = None,
        model_name: str = "",
        mode: str = "",
        iteration: str = "",
        # Asymmetric-specific parameters
        asymmetric_config: Optional[AsymmetricConfig] = None,  # Use the config object
        log_level: str = "INFO",
        # Evaluation noise parameter for multiprocessing contexts
        evaluation_noise_scale: float = 0.0,
        **kwargs
    ):
        """Initialize the asymmetric trading environment.
        
        Args:
            df: Market data DataFrame
            stock_dim: Number of stocks
            hmax: Maximum number of shares to trade
            initial_amount: Initial cash amount
            num_stock_shares: Initial stock holdings
            buy_cost_pct: Buy transaction costs per stock
            sell_cost_pct: Sell transaction costs per stock
            reward_scaling: Reward scaling factor
            state_space: State space dimension
            action_space: Action space dimension
            tech_indicator_list: List of technical indicators
            turbulence_threshold: Turbulence threshold for risk management
            risk_indicator_col: Column name for risk indicator
            make_plots: Whether to generate plots
            print_verbosity: Printing frequency
            day: Starting day
            initial: Whether this is initial setup
            previous_state: Previous state for continuation
            model_name: Model name for logging
            mode: Mode (train/test)
            iteration: Iteration identifier
            asymmetric_ratio: Target upside/downside ratio
            volatility_lookback: Lookback period for volatility calculation
            momentum_threshold: Threshold for momentum detection
            risk_adjustment_factor: Factor for risk adjustment
            upside_reward_multiplier: Multiplier for upside rewards
            downside_penalty_multiplier: Multiplier for downside penalties
        """
        
        # CRITICAL FIX: Initialize memory attributes UNCONDITIONALLY at the very beginning
        # This prevents AttributeError when step() is called, regardless of parent class behavior
        self.state_memory = []
        self.actions_memory = []
        self.date_memory = []
        self.asset_memory = []
        self.rewards_memory = []
        
        # Set default values if not provided
        if tech_indicator_list is None:
            # Use centralized tech indicator list from settings
            if settings is not None:
                tech_indicator_list = settings.data.tech_indicator_list
            else:
                # Fallback to comprehensive list if settings unavailable (using original column names)
                tech_indicator_list = [
                    'sma_5', 'sma_10', 'sma_20', 'sma_50',
                    'ema_12', 'ema_26',
                    'rsi_14', 'cci_20',
                    'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
                    'adx_14', 'dmp_14', 'dmn_14',
                    'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0',
                    'obv',
                    'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d',
                    'volume_ma_20', 'volume_ratio', 'volatility_20d',
                    'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
                    'vix_change', 'vix_change_5d', 'vix_regime_numeric',
                    'turbulence'
                ]
        
        if previous_state is None:
            previous_state = []

        # CRITICAL FIX: Validate and convert numeric parameters FIRST before any calculations
        try:
            # Convert scalar parameters first - handle numpy arrays properly
            stock_dim = int(float(stock_dim.item() if isinstance(stock_dim, np.ndarray) else stock_dim))
            hmax = int(float(hmax.item() if isinstance(hmax, np.ndarray) else hmax))
            initial_amount = int(float(initial_amount.item() if isinstance(initial_amount, np.ndarray) else initial_amount))
            reward_scaling = float(reward_scaling.item() if isinstance(reward_scaling, np.ndarray) else reward_scaling)

            # CRITICAL FIX: Ensure all arrays have exactly stock_dim elements to prevent broadcasting errors
            # Convert num_stock_shares to list of integers with exact length
            if isinstance(num_stock_shares, (list, tuple, np.ndarray)):
                # Handle numpy arrays, lists, and tuples
                if isinstance(num_stock_shares, np.ndarray):
                    num_stock_shares = num_stock_shares.tolist()
                num_stock_shares = [int(float(x)) for x in num_stock_shares]
                # Ensure exact length match
                if len(num_stock_shares) < stock_dim:
                    # Pad with zeros if too short
                    num_stock_shares.extend([0] * (stock_dim - len(num_stock_shares)))
                elif len(num_stock_shares) > stock_dim:
                    # Truncate if too long
                    num_stock_shares = num_stock_shares[:stock_dim]
            else:
                # Single value - replicate for all stocks
                single_value = int(float(num_stock_shares.item() if isinstance(num_stock_shares, np.ndarray) else num_stock_shares))
                num_stock_shares = [single_value] * stock_dim

            # Convert cost percentages to lists of floats with exact length
            if isinstance(buy_cost_pct, (list, tuple, np.ndarray)):
                # Handle numpy arrays, lists, and tuples
                if isinstance(buy_cost_pct, np.ndarray):
                    buy_cost_pct = buy_cost_pct.tolist()
                buy_cost_pct = [float(x) for x in buy_cost_pct]
                # Ensure exact length match
                if len(buy_cost_pct) < stock_dim:
                    # Pad with last value if too short
                    last_value = buy_cost_pct[-1] if buy_cost_pct else 0.001
                    buy_cost_pct.extend([last_value] * (stock_dim - len(buy_cost_pct)))
                elif len(buy_cost_pct) > stock_dim:
                    # Truncate if too long
                    buy_cost_pct = buy_cost_pct[:stock_dim]
            else:
                # Single value - replicate for all stocks
                single_value = float(buy_cost_pct.item() if isinstance(buy_cost_pct, np.ndarray) else buy_cost_pct)
                buy_cost_pct = [single_value] * stock_dim

            if isinstance(sell_cost_pct, (list, tuple, np.ndarray)):
                # Handle numpy arrays, lists, and tuples
                if isinstance(sell_cost_pct, np.ndarray):
                    sell_cost_pct = sell_cost_pct.tolist()
                sell_cost_pct = [float(x) for x in sell_cost_pct]
                # Ensure exact length match
                if len(sell_cost_pct) < stock_dim:
                    # Pad with last value if too short
                    last_value = sell_cost_pct[-1] if sell_cost_pct else 0.001
                    sell_cost_pct.extend([last_value] * (stock_dim - len(sell_cost_pct)))
                elif len(sell_cost_pct) > stock_dim:
                    # Truncate if too long
                    sell_cost_pct = sell_cost_pct[:stock_dim]
            else:
                # Single value - replicate for all stocks
                single_value = float(sell_cost_pct.item() if isinstance(sell_cost_pct, np.ndarray) else sell_cost_pct)
                sell_cost_pct = [single_value] * stock_dim

        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid numeric parameters provided: {e}")

        # Calculate state and action space if not provided
        # CRITICAL FIX: Calculate state dimensions regardless of whether state_space is provided
        # These dimensions are needed later for validation
        base_state = 1 + 2 * stock_dim  # cash + holdings + prices
        tech_state = len(tech_indicator_list) * stock_dim  # tech indicators per stock
        asymmetric_state = stock_dim * 3  # volatility + momentum + asymmetry_score per stock
        
        if state_space is None:
            # CRITICAL FIX: Calculate state space based on actual parent environment dimensions
            # The parent StockTradingEnv calculates: cash + holdings + prices + tech_indicators
            # We need to match this exactly and then add our asymmetric features
            state_space = base_state + tech_state + asymmetric_state

        if action_space is None:
            action_space = stock_dim  # one action per stock
        
        # Store action space dimensions
        self.action_space_dim = action_space
        self.action_space = spaces.Box(low=-1, high=1, shape=(action_space,), dtype=np.float32)
        
        # Store asymmetric parameters from config
        if asymmetric_config is None:
            asymmetric_config = AsymmetricConfig(symbols=[])
        self.asymmetric_ratio = asymmetric_config.target_upside_downside_ratio
        self.volatility_lookback = asymmetric_config.volatility_lookback
        self.momentum_threshold = asymmetric_config.momentum_threshold
        self.risk_adjustment_factor = 0.5  # Fixed value for risk adjustment
        self.upside_reward_multiplier = 1.5  # Fixed value for upside reward
        self.downside_penalty_multiplier = 1.0  # Fixed value for downside penalty
        
        # Initialize logger with specified log level
        # CRITICAL FIX: Use standard Python logger for pickle compatibility
        from utils.logging import get_standard_logger
        self.log_level = log_level
        self.logger = get_standard_logger(self.__class__.__name__, level=log_level)
        

        # CRITICAL FIX: Initialize episode attribute BEFORE parent class initialization
        # The parent class constructor or its methods might call reset() which expects this attribute
        self.episode = 0
        self.logger.debug("Pre-initialized episode attribute to 0 before parent class initialization")

        # CRITICAL FIX: Initialize state_space attribute BEFORE parent class initialization
        # The _get_state() method expects this attribute to exist
        # Calculate enhanced state space: base state + asymmetric features
        base_state_size = 1 + 2 * stock_dim  # cash + holdings + prices
        tech_state_size = len(tech_indicator_list) * stock_dim
        asymmetric_features_size = stock_dim * 3  # volatility, momentum, asymmetry per stock
        self.state_space = base_state_size + tech_state_size + asymmetric_features_size
        self.enhanced_state_space = self.state_space  # Set enhanced_state_space early
        self.logger.debug(f"Pre-initialized state_space to {self.state_space} (base: {base_state_size}, tech: {tech_state_size}, asymmetric: {asymmetric_features_size})")

        # Log the successful parameter validation
        self.logger.info(f"Parameter validation successful - stock_dim: {stock_dim}, hmax: {hmax}, initial_amount: {initial_amount}")
        self.logger.info(f"num_stock_shares: {num_stock_shares}, buy_cost_pct: {buy_cost_pct}, sell_cost_pct: {sell_cost_pct}")

        # CRITICAL DEBUG: Log exact values and types before parent class initialization
        self.logger.info(f"DEBUG: About to call super().__init__() with:")
        self.logger.info(f"  stock_dim: {stock_dim} (type: {type(stock_dim)})")
        self.logger.info(f"  hmax: {hmax} (type: {type(hmax)})")
        self.logger.info(f"  initial_amount: {initial_amount} (type: {type(initial_amount)})")
        self.logger.info(f"  num_stock_shares: {num_stock_shares} (type: {type(num_stock_shares)}, element types: {[type(x) for x in num_stock_shares] if isinstance(num_stock_shares, list) else 'N/A'})")
        self.logger.info(f"  buy_cost_pct: {buy_cost_pct} (type: {type(buy_cost_pct)}, element types: {[type(x) for x in buy_cost_pct] if isinstance(buy_cost_pct, list) else 'N/A'})")
        self.logger.info(f"  sell_cost_pct: {sell_cost_pct} (type: {type(sell_cost_pct)}, element types: {[type(x) for x in sell_cost_pct] if isinstance(sell_cost_pct, list) else 'N/A'})")
        self.logger.info(f"  reward_scaling: {reward_scaling} (type: {type(reward_scaling)})")

        # CRITICAL FIX: Convert to numpy arrays to prevent parent class from overriding with strings
        # The parent class can't override numpy arrays as easily as lists
        converted_num_stock_shares = np.array(num_stock_shares, dtype=int)
        converted_buy_cost_pct = np.array(buy_cost_pct, dtype=float)
        converted_sell_cost_pct = np.array(sell_cost_pct, dtype=float)

        # CRITICAL VALIDATION: Ensure all arrays have exactly the same length
        if len(converted_num_stock_shares) != stock_dim:
            raise ValueError(f"num_stock_shares array length ({len(converted_num_stock_shares)}) != stock_dim ({stock_dim})")
        if len(converted_buy_cost_pct) != stock_dim:
            raise ValueError(f"buy_cost_pct array length ({len(converted_buy_cost_pct)}) != stock_dim ({stock_dim})")
        if len(converted_sell_cost_pct) != stock_dim:
            raise ValueError(f"sell_cost_pct array length ({len(converted_sell_cost_pct)}) != stock_dim ({stock_dim})")

        self.logger.info(f"NUMPY CONVERSION: converted_num_stock_shares = {converted_num_stock_shares} (type: {type(converted_num_stock_shares)}, dtype: {converted_num_stock_shares.dtype}, shape: {converted_num_stock_shares.shape})")
        self.logger.info(f"NUMPY CONVERSION: converted_buy_cost_pct = {converted_buy_cost_pct} (type: {type(converted_buy_cost_pct)}, dtype: {converted_buy_cost_pct.dtype}, shape: {converted_buy_cost_pct.shape})")
        self.logger.info(f"NUMPY CONVERSION: converted_sell_cost_pct = {converted_sell_cost_pct} (type: {type(converted_sell_cost_pct)}, dtype: {converted_sell_cost_pct.dtype}, shape: {converted_sell_cost_pct.shape})")

        # CRITICAL FIX: Initialize required attributes before calling parent __init__
        # The parent class expects certain attributes to be available during initialization
        self.df = df
        self.stock_dim = stock_dim
        self.hmax = hmax
        self.initial_amount = initial_amount
        self.num_stock_shares = converted_num_stock_shares
        self.buy_cost_pct = converted_buy_cost_pct
        self.sell_cost_pct = converted_sell_cost_pct
        self.reward_scaling = reward_scaling
        self.tech_indicator_list = tech_indicator_list
        self.turbulence_threshold = turbulence_threshold
        self.risk_indicator_col = risk_indicator_col
        self.make_plots = make_plots
        self.print_verbosity = print_verbosity
        self.day = day
        self.initial = initial
        self.previous_state = previous_state if previous_state else []
        self.model_name = model_name
        self.mode = mode
        self.iteration = iteration

        # Initialize data for the current day
        # CRITICAL FIX: Ensure self.data is a pandas Series with proper structure
        try:
            day_data = self.df.loc[self.day, :]
            if isinstance(day_data, pd.DataFrame):
                # If multiple rows, take the first one and ensure it's a Series
                self.data = day_data.iloc[0].copy()
            else:
                # Already a Series, make a copy
                self.data = day_data.copy()

            # CRITICAL FIX: Ensure date field is properly structured for parent class
            # The parent class expects self.data.date to be a Series-like object with .unique() method
            if 'date' in self.data:
                # Convert single date to a proper pandas Series
                date_value = self.data['date']
                if not hasattr(date_value, 'unique'):
                    # Create a proper pandas Series for the date
                    self.data['date'] = pd.Series([date_value], name='date')
        except Exception as e:
            self.logger.warning(f"Could not properly structure data: {e}. Using first row.")
            self.data = self.df.iloc[0].copy()
            # Add mock date if missing
            if 'date' not in self.data:
                self.data['date'] = self.df.iloc[0]['date'] if 'date' in self.df.columns else pd.Timestamp.now()

        # CRITICAL FIX: Pre-initialize state to prevent NoneType errors
        # Calculate the expected state size and initialize with zeros
        base_state_size = 1 + 2 * stock_dim  # cash + holdings + prices
        tech_state_size = len(tech_indicator_list) * stock_dim
        total_base_state_size = base_state_size + tech_state_size

        # Initialize state with proper structure
        initial_state = [float(initial_amount)]  # cash
        initial_state.extend([0.0] * stock_dim)  # holdings

        # Add initial prices from data
        try:
            if isinstance(self.data, pd.Series):
                # Single row of data
                initial_state.append(float(self.data['close']))
                # Pad with the same price for remaining stocks
                initial_state.extend([float(self.data['close'])] * (stock_dim - 1))
            else:
                # Multiple rows - get first stock's price
                first_close = float(self.data.iloc[0]['close']) if len(self.data) > 0 else 100.0
                initial_state.extend([first_close] * stock_dim)
        except Exception as e:
            self.logger.warning(f"Could not get initial prices from data: {e}. Using default.")
            initial_state.extend([100.0] * stock_dim)  # Default prices

        # Add technical indicators
        try:
            for tech in tech_indicator_list:
                if isinstance(self.data, pd.Series) and tech in self.data:
                    initial_state.append(float(self.data[tech]))
                elif hasattr(self.data, 'iloc') and len(self.data) > 0 and tech in self.data.columns:
                    initial_state.append(float(self.data.iloc[0][tech]))
                else:
                    initial_state.append(0.0)  # Default value
        except Exception as e:
            self.logger.warning(f"Could not initialize technical indicators: {e}. Using defaults.")
            initial_state.extend([0.0] * len(tech_indicator_list))

        # Set the state
        self.state = initial_state
        self.logger.info(f"Pre-initialized state with {len(self.state)} elements")

        # Initialize parent class
        try:
            super().__init__(
                df=df,
                stock_dim=stock_dim,
                hmax=hmax,
                initial_amount=initial_amount,
                num_stock_shares=converted_num_stock_shares,  # Use numpy arrays
                buy_cost_pct=converted_buy_cost_pct,          # Use numpy arrays
                sell_cost_pct=converted_sell_cost_pct,        # Use numpy arrays
                reward_scaling=reward_scaling,
                state_space=state_space,
                action_space=action_space,
                tech_indicator_list=tech_indicator_list,
                turbulence_threshold=turbulence_threshold,
                risk_indicator_col=risk_indicator_col,
                make_plots=make_plots,
                print_verbosity=print_verbosity,
                day=day,
                initial=initial,
                previous_state=previous_state,
                model_name=model_name,
                mode=mode,
                iteration=iteration
            )

            # CRITICAL FIX: Restore integer index after parent class corrupts it with DatetimeIndex
            # The parent FinRL class converts integer indices to DatetimeIndex using pd.to_datetime()
            # which interprets integers as nanoseconds since epoch, creating 1970-01-01 dates
            if hasattr(self, 'df') and isinstance(self.df.index, pd.DatetimeIndex):
                # Check if the DatetimeIndex starts with 1970 (indicating integer corruption)
                if str(self.df.index.min()).startswith('1970'):
                    self.logger.warning("FinRL parent class corrupted integer index to 1970 DatetimeIndex. Restoring integer index.")
                    # Store the number of unique trading days BEFORE index manipulation
                    self.unique_trading_days = len(self.df['date'].unique()) if 'date' in self.df.columns else len(self.df.index.unique())
                    
                    # Restore original integer index from the day column or recreate it
                    if 'day' in self.df.columns:
                        # Use the day column to restore integer index
                        unique_days = sorted(self.df['day'].unique())
                        day_to_int = {day: i for i, day in enumerate(unique_days)}
                        self.df['original_day'] = self.df['day'].map(day_to_int)
                        self.df = self.df.set_index('original_day')
                        self.df.index.name = None  # Remove index name for consistency
                        self.logger.info(f"Restored integer index with {len(self.df.index.unique())} unique values")
                    else:
                        # Fallback: create sequential integer index
                        self.df = self.df.reset_index(drop=True)
                        self.logger.info(f"Created sequential integer index with {len(self.df)} rows")
                else:
                    # DatetimeIndex is valid, calculate trading days
                    self.unique_trading_days = len(self.df.index.unique())
            else:
                # Integer index or other type, calculate trading days from date column or index
                self.unique_trading_days = len(self.df['date'].unique()) if 'date' in self.df.columns else len(self.df.index.unique())

            # CRITICAL FIX: Ensure df attribute is properly set after parent initialization
            # The parent class should have set this, but let's make sure
            if not hasattr(self, 'df') or self.df is None:
                self.df = df
                self.logger.warning("df attribute was not set by parent class, setting it manually")

            # Memory attributes are now initialized unconditionally at the beginning of __init__
            self.logger.debug("Memory attributes (state_memory, actions_memory, etc.) already initialized")

            # Episode attribute should already be initialized before parent class initialization
            self.logger.debug(f"Episode attribute after parent initialization: {getattr(self, 'episode', 'NOT_SET')}")

        except Exception as e:
            self.logger.error(f"Failed to initialize parent StockTradingEnv: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Memory attributes are already initialized unconditionally at the beginning of __init__
            self.logger.debug("Memory attributes already exist, no need to initialize in exception handler")
            
            raise
        
        # CRITICAL FIX: Use calculated state space without forcing reset during initialization
        # The parent class initialization must complete before we can safely call reset
        enhanced_state_space = state_space
        self.enhanced_state_space = enhanced_state_space  # Store as instance attribute
        self.logger.info(f"Using calculated enhanced state space: {enhanced_state_space}")

        # Store dimensions for later validation
        self.asymmetric_features_size = stock_dim * 3
        self.calculated_base_state_size = base_state + tech_state

        # Override observation_space to match the enhanced state space
        # The enhanced state includes base state + asymmetric features
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(enhanced_state_space,),
            dtype=np.float32
        )
        
        # Asymmetric-specific state tracking
        self.price_history = {}
        self.volatility_history = {}
        self.momentum_history = {}
        self.asymmetry_scores = {}
        self.previous_portfolio_value = self.initial_amount
        
        # Initialize price history with current prices
        self._initialize_price_history()
        
        # 🚨 CRITICAL DEBUG: Check what DataFrame we received after index restoration
        self.logger.info(f"🚨 DEBUG ENV: DataFrame shape: {self.df.shape}")
        self.logger.info(f"🚨 DEBUG ENV: Index type: {type(self.df.index)}")
        self.logger.info(f"🚨 DEBUG ENV: Unique index values: {len(self.df.index.unique())}")
        self.logger.info(f"🚨 DEBUG ENV: Index range: {self.df.index.min()} to {self.df.index.max()}")
        if 'date' in self.df.columns:
            self.logger.info(f"🚨 DEBUG ENV: Date range: {self.df['date'].min()} to {self.df['date'].max()}")
            self.logger.info(f"🚨 DEBUG ENV: Unique dates: {self.df['date'].nunique()}")
        
        # Verify the index fix worked
        if isinstance(self.df.index, pd.DatetimeIndex) and str(self.df.index.min()).startswith('1970'):
            self.logger.error(f"❌ INDEX CORRUPTION STILL PRESENT: {self.df.index.min()} to {self.df.index.max()}")
        else:
            self.logger.info(f"✅ INDEX FIX SUCCESSFUL: Using proper integer index")
        
                
        # Add max_step attribute for ElegantRL compatibility
        # Use the correct number of unique trading days, not total rows
        self.max_step = self.unique_trading_days
        self.logger.info(f"🔧 Set max_step = {self.max_step} for ElegantRL compatibility (unique trading days)")
        self.logger.info(f"📊 Data structure: {len(self.df)} total rows, {self.unique_trading_days} unique trading days")
        self.logger.info(f"AsymmetricTradingEnv initialized with {stock_dim} stocks")
        self.logger.info(f"Asymmetric ratio: {self.asymmetric_ratio}, Volatility lookback: {self.volatility_lookback}")
        
        # CRITICAL FIX: Enable centralized evaluation noise for multiprocessing contexts
        # Each worker process needs to enable the noise manager independently
        if evaluation_noise_scale > 0:
            try:
                from utils.evaluation_noise import EvaluationNoiseManager
                EvaluationNoiseManager.enable(noise_scale=evaluation_noise_scale)
                self.logger.info(f"✅ Enabled centralized evaluation noise in worker process (scale={evaluation_noise_scale})")
            except Exception as e:
                self.logger.warning(f"Could not enable evaluation noise in worker process: {e}")
        else:
            self.logger.debug("Evaluation noise not enabled (scale=0.0)")

    def _get_date(self):
        """Override parent class _get_date method to handle Timestamp objects.

        The parent class expects self.data.date to have a unique() method,
        but when self.data is a pandas Series, self.data.date is a Timestamp
        object which doesn't have unique(). This method handles both cases.

        Returns:
            Date value (Timestamp or similar)
        """
        try:
            if hasattr(self, 'data') and self.data is not None:
                if hasattr(self.data, 'date'):
                    date_field = self.data.date
                    # Check if it has unique() method (Series case)
                    if hasattr(date_field, 'unique'):
                        return date_field.unique()[0]
                    else:
                        # Direct Timestamp case
                        return date_field
                elif 'date' in self.data:
                    # Alternative access method
                    date_field = self.data['date']
                    if hasattr(date_field, 'unique'):
                        return date_field.unique()[0]
                    else:
                        return date_field

            # Fallback: use current date from df if available
            if hasattr(self, 'df') and hasattr(self, 'day'):
                try:
                    day_data = self.df.loc[self.day, :]
                    if isinstance(day_data, pd.DataFrame) and 'date' in day_data.columns:
                        return day_data['date'].iloc[0]
                    elif isinstance(day_data, pd.Series) and 'date' in day_data:
                        return day_data['date']
                except Exception:
                    pass

            # Final fallback
            return pd.Timestamp.now()

        except Exception as e:
            self.logger.warning(f"Error in _get_date: {e}. Using current timestamp.")
            return pd.Timestamp.now()

    def _initialize_price_history(self):
        """Initialize price history for volatility and momentum calculations."""
        try:
            # CRITICAL FIX: Don't rely on self.state during initialization as it may not be available yet
            # Instead, initialize with default values and update later during first reset/step
            if hasattr(self, 'df') and self.df is not None:
                tickers = self.df.tic.unique()
                for i, tic in enumerate(tickers[:self.stock_dim]):
                    # Use a default price from the first row of data if available
                    try:
                        if hasattr(self, 'data') and self.data is not None and 'close' in self.data:
                            default_price = float(self.data['close'])
                        else:
                            # Get price from first row of dataframe for this ticker
                            ticker_data = self.df[self.df.tic == tic]
                            if not ticker_data.empty and 'close' in ticker_data.columns:
                                default_price = float(ticker_data.iloc[0]['close'])
                            else:
                                default_price = 100.0  # Fallback default
                    except Exception:
                        default_price = 100.0  # Fallback default

                    self.price_history[tic] = [default_price] * self.volatility_lookback
                    self.volatility_history[tic] = [0.0]
                    self.momentum_history[tic] = [0.0]
                    self.asymmetry_scores[tic] = 0.0
            else:
                # Fallback initialization with generic tickers
                for i in range(self.stock_dim):
                    tic = f"stock_{i}"
                    self.price_history[tic] = [100.0] * self.volatility_lookback  # Default price
                    self.volatility_history[tic] = [0.0]
                    self.momentum_history[tic] = [0.0]
                    self.asymmetry_scores[tic] = 0.0

            self.logger.debug(f"Initialized price history for {len(self.price_history)} tickers")
        except Exception as e:
            self.logger.warning(f"Could not initialize price history: {e}")
            # Initialize with empty dictionaries as fallback
            for i in range(self.stock_dim):
                tic = f"stock_{i}"
                self.price_history[tic] = [100.0] * self.volatility_lookback  # Default price
                self.volatility_history[tic] = [0.0]
                self.momentum_history[tic] = [0.0]
                self.asymmetry_scores[tic] = 0.0
    
    def _update_price_history(self):
        """Update price history for volatility and momentum calculations."""
        try:
            if len(self.df.tic.unique()) > 1:
                # Multiple stocks
                for i, tic in enumerate(self.df.tic.unique()):
                    if i < len(self.state[1:1+self.stock_dim]):
                        current_price = self.state[1 + i]
                        if tic in self.price_history:
                            self.price_history[tic].append(current_price)
                            if len(self.price_history[tic]) > self.volatility_lookback:
                                self.price_history[tic].pop(0)
            else:
                # Single stock
                tic = self.df.tic.unique()[0]
                current_price = self.state[1]
                if tic in self.price_history:
                    self.price_history[tic].append(current_price)
                    if len(self.price_history[tic]) > self.volatility_lookback:
                        self.price_history[tic].pop(0)
        except Exception as e:
            self.logger.warning(f"Could not update price history: {e}")
    
    def _calculate_volatility(self, tic: str) -> float:
        """Calculate volatility for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Volatility measure
        """
        self.logger.debug(f"DEBUG: _calculate_volatility called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 2:
                self.logger.debug(f"DEBUG: _calculate_volatility returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            # CRITICAL FIX: Use safe division to avoid runtime warnings
            # Replace divide by zero with 0.0 and invalid operations with 0.0
            with np.errstate(divide='ignore', invalid='ignore'):
                returns = np.divide(np.diff(prices), prices[:-1], out=np.zeros_like(np.diff(prices)), where=prices[:-1]!=0)
            
            # CRITICAL FIX: Check for NaN/inf in returns calculation
            if np.any(np.isnan(returns)) or np.any(np.isinf(returns)):
                self.logger.debug(f"NaN/inf detected in returns for {tic}: {returns}. Using 0.0 volatility")
                volatility = 0.0
            else:
                volatility = np.std(returns) if len(returns) > 1 else 0.0
                
            # Additional safety check
            if np.isnan(volatility) or np.isinf(volatility):
                self.logger.debug(f"NaN/inf detected in calculated volatility for {tic}: {volatility}. Using 0.0")
                volatility = 0.0
            
            # Update volatility history
            if tic in self.volatility_history:
                self.volatility_history[tic].append(volatility)
                if len(self.volatility_history[tic]) > 10:  # Keep last 10 volatility measures
                    self.volatility_history[tic].pop(0)
            
            self.logger.debug(f"DEBUG: _calculate_volatility for {tic}: {volatility}")
            return volatility
        except Exception as e:
            self.logger.debug(f"Could not calculate volatility for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_volatility returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _calculate_momentum(self, tic: str) -> float:
        """Calculate momentum for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Momentum measure
        """
        self.logger.debug(f"DEBUG: _calculate_momentum called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 5:
                self.logger.debug(f"DEBUG: _calculate_momentum returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            
            # CRITICAL FIX: Check for NaN/inf in prices
            if np.any(np.isnan(prices)) or np.any(np.isinf(prices)):
                self.logger.debug(f"NaN/inf detected in prices for {tic}: {prices}. Using 0.0 momentum")
                momentum = 0.0
            else:
                # Calculate momentum as percentage change over lookback period
                momentum = (prices[-1] - prices[0]) / prices[0] if prices[0] != 0 else 0.0
                
            # Additional safety check
            if np.isnan(momentum) or np.isinf(momentum):
                self.logger.debug(f"NaN/inf detected in calculated momentum for {tic}: {momentum}. Using 0.0")
                momentum = 0.0
            
            # Update momentum history
            if tic in self.momentum_history:
                self.momentum_history[tic].append(momentum)
                if len(self.momentum_history[tic]) > 10:  # Keep last 10 momentum measures
                    self.momentum_history[tic].pop(0)
            
            self.logger.debug(f"DEBUG: _calculate_momentum for {tic}: {momentum}")
            return momentum
        except Exception as e:
            self.logger.debug(f"Could not calculate momentum for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_momentum returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _calculate_asymmetry_score(self, tic: str) -> float:
        """Calculate asymmetry score for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Asymmetry score (higher = more favorable for asymmetric strategy)
        """
        self.logger.debug(f"DEBUG: _calculate_asymmetry_score called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 3:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            
            # CRITICAL FIX: Check for NaN/inf in prices
            if np.any(np.isnan(prices)) or np.any(np.isinf(prices)):
                self.logger.debug(f"NaN/inf detected in prices for {tic}: {prices}. Using 0.0 asymmetry")
                return 0.0
                
            # CRITICAL FIX: Use safe division to avoid runtime warnings
            # Replace divide by zero with 0.0 and invalid operations with 0.0
            with np.errstate(divide='ignore', invalid='ignore'):
                returns = np.divide(np.diff(prices), prices[:-1], out=np.zeros_like(np.diff(prices)), where=prices[:-1]!=0)
            
            # CRITICAL FIX: Check for NaN/inf in returns
            if np.any(np.isnan(returns)) or np.any(np.isinf(returns)):
                self.logger.debug(f"NaN/inf detected in returns for {tic}: {returns}. Using 0.0 asymmetry")
                return 0.0
            
            if len(returns) < 2:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (insufficient returns)")
                return 0.0
            
            # Calculate upside and downside returns
            upside_returns = returns[returns > 0]
            downside_returns = returns[returns < 0]
            
            if len(upside_returns) == 0 or len(downside_returns) == 0:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (no upside or downside returns)")
                return 0.0
            
            # Calculate asymmetry score based on upside/downside ratio
            avg_upside = np.mean(upside_returns)
            avg_downside = np.abs(np.mean(downside_returns))
            
            # CRITICAL FIX: Check for NaN/inf in averages
            if np.isnan(avg_upside) or np.isinf(avg_upside) or np.isnan(avg_downside) or np.isinf(avg_downside):
                self.logger.debug(f"NaN/inf detected in averages for {tic}: upside={avg_upside}, downside={avg_downside}. Using 0.0 asymmetry")
                return 0.0
            
            asymmetry_score = avg_upside / avg_downside if avg_downside != 0 else 0.0
            
            # CRITICAL FIX: Check for NaN/inf in asymmetry score
            if np.isnan(asymmetry_score) or np.isinf(asymmetry_score):
                self.logger.debug(f"NaN/inf detected in asymmetry_score for {tic}: {asymmetry_score}. Using 0.0")
                return 0.0
            
            # Normalize score relative to target ratio
            normalized_score = asymmetry_score / self.asymmetric_ratio
            
            # CRITICAL FIX: Final check for NaN/inf in normalized score
            if np.isnan(normalized_score) or np.isinf(normalized_score):
                self.logger.debug(f"NaN/inf detected in normalized_score for {tic}: {normalized_score}. Using 0.0")
                return 0.0
            
            self.asymmetry_scores[tic] = normalized_score
            
            self.logger.debug(f"DEBUG: _calculate_asymmetry_score for {tic}: {normalized_score}")
            return normalized_score
            
        except Exception as e:
            self.logger.debug(f"Could not calculate asymmetry score for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _get_asymmetric_state_features(self) -> np.ndarray:
        """Get asymmetric-specific state features.
        
        Returns:
            Array of asymmetric features
        """
        self.logger.debug("DEBUG: _get_asymmetric_state_features called")
        features = []
        
        try:
            tickers = self.df.tic.unique() if len(self.df.tic.unique()) > 1 else [self.df.tic.unique()[0]]
            self.logger.debug(f"DEBUG: Processing {len(tickers)} tickers: {tickers[:self.stock_dim]}")
            
            for tic in tickers[:self.stock_dim]:  # Ensure we don't exceed stock_dim
                volatility = self._calculate_volatility(tic)
                momentum = self._calculate_momentum(tic)
                asymmetry = self._calculate_asymmetry_score(tic)
                
                # CRITICAL FIX: Aggressive feature value clamping
                # Clamp to reasonable ranges to prevent extreme values
                volatility = np.clip(volatility, 0.0, 2.0)  # Max 200% volatility
                momentum = np.clip(momentum, -2.0, 2.0)    # Max ±200% momentum  
                asymmetry = np.clip(asymmetry, 0.0, 5.0)   # Max 5x asymmetry ratio
                
                # Check for NaN/inf values and replace with safe defaults
                if np.isnan(volatility) or np.isinf(volatility):
                    volatility = 0.0
                    
                if np.isnan(momentum) or np.isinf(momentum):
                    momentum = 0.0
                    
                if np.isnan(asymmetry) or np.isinf(asymmetry):
                    asymmetry = 0.0
                
                features.extend([volatility, momentum, asymmetry])
                self.logger.debug(f"DEBUG: Features for {tic}: vol={volatility}, mom={momentum}, asym={asymmetry}")
            
            # Pad with zeros if we have fewer stocks than stock_dim
            while len(features) < self.stock_dim * 3:
                features.extend([0.0, 0.0, 0.0])
                
            self.logger.debug(f"DEBUG: Final asymmetric features array length: {len(features)}")
            
            # Convert to numpy array and perform final NaN check
            features_array = np.array(features, dtype=np.float32)
            
            # CRITICAL FIX: Aggressive cleaning of asymmetric features
            # Convert extreme values and clean thoroughly
            features_array = np.nan_to_num(features_array, nan=0.0, posinf=10.0, neginf=-10.0)
            features_array = np.clip(features_array, -10.0, 10.0)
            
            # Final validation
            if np.any(np.isnan(features_array)) or np.any(np.isinf(features_array)):
                self.logger.warning(f"Residual NaN/inf in asymmetric features after cleaning")
                features_array = np.zeros_like(features_array)
                
        except Exception as e:
            self.logger.debug(f"Could not calculate asymmetric features: {e}")
            self.logger.debug(f"DEBUG: _get_asymmetric_state_features returning zeros due to exception")
            # Return zeros as fallback
            features_array = np.array([0.0] * (self.stock_dim * 3), dtype=np.float32)
        
        return features_array
    
    def _update_state(self):
        """Update state with asymmetric features."""
        try:
            # Get base state from parent class
            base_state = super()._update_state()
            
            if base_state is None:
                self.logger.error("Base state is None from parent class")
                return None
            
            # Update price history
            self._update_price_history()
            
            # Get asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()
            
            # Combine base state with asymmetric features
            enhanced_state = np.concatenate([base_state, asymmetric_features])
            
            return enhanced_state
            
        except Exception as e:
            self.logger.error(f"Error in _update_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def _initiate_state(self):
        """Initialize state with asymmetric features."""
        try:
            # CRITICAL FIX: Handle the parent class data structure issues
            # The parent class expects self.data to be a pandas Series, but it might be a DataFrame or scalar

            # Ensure self.data is properly structured
            if hasattr(self, 'df') and hasattr(self, 'day'):
                try:
                    # Handle different index types (integer vs DatetimeIndex)
                    if isinstance(self.df.index, pd.DatetimeIndex):
                        # Use iloc for DatetimeIndex during initialization
                        if self.day < len(self.df.index.unique()):
                            unique_dates = sorted(self.df.index.unique())
                            target_date = unique_dates[self.day]
                            day_data = self.df.loc[target_date, :]
                        else:
                            # Fallback to first available data
                            day_data = self.df.iloc[0]
                            self.logger.debug(f"Day {self.day} out of range, using first row")
                    else:
                        # Integer index - try direct access
                        day_data = self.df.loc[self.day, :]
                    
                    if isinstance(day_data, pd.DataFrame):
                        # If multiple rows, take the first one
                        self.data = day_data.iloc[0]
                    else:
                        # Already a Series
                        self.data = day_data
                        
                except (KeyError, IndexError, Exception) as e:
                    self.logger.debug(f"Could not get day data for day {self.day}: {e}. Using first row.")
                    self.data = self.df.iloc[0]

            # CRITICAL FIX: Manually construct the state instead of calling parent _initiate_state
            # This avoids the data structure issues in the parent class
            state = []

            # Add initial cash
            state.append(float(self.initial_amount))

            # Add initial stock holdings (should be zeros or provided values)
            if hasattr(self, 'num_stock_shares'):
                for i in range(self.stock_dim):
                    if i < len(self.num_stock_shares):
                        state.append(float(self.num_stock_shares[i]))
                    else:
                        state.append(0.0)
            else:
                state.extend([0.0] * self.stock_dim)

            # Add stock prices - get individual prices for each stock
            try:
                if hasattr(self, 'df') and self.df is not None:
                    # Get data for current day (day 0 for initialization)
                    current_day = getattr(self, 'day', 0)
                    day_data = self.df.loc[current_day]
                    
                    if isinstance(day_data, pd.DataFrame) and len(day_data) > 1:
                        # Multiple stocks - get price for each stock
                        if 'close' in day_data.columns and 'tic' in day_data.columns:
                            # Sort by tic to ensure consistent order
                            day_data = day_data.sort_values('tic')
                            prices = day_data['close'].tolist()[:self.stock_dim]
                            # Pad with last price if needed
                            while len(prices) < self.stock_dim:
                                prices.append(prices[-1] if prices else 100.0)
                            state.extend(prices)
                            self.logger.debug(f"Using individual stock prices: {prices}")
                        else:
                            state.extend([100.0] * self.stock_dim)
                    elif isinstance(day_data, pd.Series) and 'close' in day_data:
                        # Single stock - use same price for all (fallback)
                        price = float(day_data['close'])
                        state.extend([price] * self.stock_dim)
                        self.logger.debug(f"Using single stock price for all: {price}")
                    else:
                        state.extend([100.0] * self.stock_dim)
                        self.logger.debug("Using default prices: 100.0")
                else:
                    state.extend([100.0] * self.stock_dim)
                    self.logger.debug("No df available, using default prices: 100.0")
            except Exception as e:
                self.logger.debug(f"Could not get stock prices: {e}. Using defaults.")
                state.extend([100.0] * self.stock_dim)

            # Add technical indicators
            try:
                if hasattr(self, 'tech_indicator_list') and self.tech_indicator_list:
                    for tech in self.tech_indicator_list:
                        try:
                            if isinstance(self.data, pd.Series) and tech in self.data:
                                state.append(float(self.data[tech]))
                            else:
                                state.append(0.0)  # Default value
                        except Exception:
                            state.append(0.0)  # Default value
            except Exception as e:
                self.logger.warning(f"Could not add technical indicators: {e}")

            # Convert to numpy array
            base_state = np.array(state)
            # self.logger.info(f"Manually constructed base state with {len(base_state)} elements")

            # Initialize asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()

            # Combine base state with asymmetric features
            enhanced_state = np.concatenate([base_state, asymmetric_features])

            return enhanced_state

        except Exception as e:
            self.logger.error(f"Error in _initiate_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return a fallback state
            fallback_size = 1 + 2 * self.stock_dim + len(self.tech_indicator_list) + (self.stock_dim * 3)
            return np.zeros(fallback_size, dtype=np.float32)
    

    def _get_state(self) -> np.ndarray:
        """Get enhanced state with asymmetric features.

        Returns:
            Enhanced state array including asymmetric features
        """
        self.logger.debug("DEBUG: _get_state called")
        try:
            # Initialize base_state to ensure it's always defined
            base_state = None
            
            # Get base state from parent class
            if hasattr(self, 'state') and self.state is not None:
                self.logger.debug(f"DEBUG: self.state type: {type(self.state)}, shape: {getattr(self.state, 'shape', 'N/A')}, dtype: {getattr(self.state, 'dtype', 'N/A')}")
                if isinstance(self.state, np.ndarray) and self.state.size < 20: # Log content if small
                    self.logger.debug(f"DEBUG: self.state content: {self.state}")
                elif not isinstance(self.state, np.ndarray):
                     self.logger.debug(f"DEBUG: self.state content (non-ndarray): {self.state}")

                # FIX: Handle inhomogeneous state arrays properly
                try:
                    if isinstance(self.state, np.ndarray):
                        # If already a numpy array, flatten it to ensure 1D
                        base_state = self.state.flatten()
                    elif isinstance(self.state, (list, tuple)):
                        # If it's a list or tuple, flatten any nested structures
                        flattened_state = []
                        for item in self.state:
                            if isinstance(item, (np.ndarray, list, tuple)):
                                # Flatten nested arrays/lists
                                if isinstance(item, np.ndarray):
                                    flattened_state.extend(item.flatten().tolist())
                                else:
                                    flattened_state.extend(np.array(item).flatten().tolist())
                            else:
                                # Scalar value
                                flattened_state.append(float(item))
                        base_state = np.array(flattened_state, dtype=np.float32)
                    else:
                        # Try direct conversion for other types
                        base_state = np.array([float(self.state)], dtype=np.float32)
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"DEBUG: Failed to convert self.state to numpy array: {e}. Using fallback.")
                    # Fallback: create a zero array of expected size
                    expected_base_size = self.state_space - (self.stock_dim * 3)  # Subtract asymmetric features
                    base_state = np.zeros(max(1, expected_base_size), dtype=np.float32)

                # CRITICAL FIX: Clean base_state immediately after processing
                if np.any(np.isnan(base_state)) or np.any(np.isinf(base_state)):
                    self.logger.warning(f"NaN/Inf detected in base_state before asymmetric features")
                    base_state = np.nan_to_num(base_state, nan=0.0, posinf=1e3, neginf=-1e3)
                    base_state = np.clip(base_state, -1e3, 1e3)
                    self.logger.warning("Cleaned base_state of NaN/Inf values")
                
                self.logger.debug(f"DEBUG: base_state (processed) type: {type(base_state)}, shape: {base_state.shape}, dtype: {base_state.dtype}")
                if base_state.size < 20: # Log content if small
                    self.logger.debug(f"DEBUG: base_state content: {base_state}")
            else:
                self.logger.error("No base state (self.state) available or is None")
                return np.zeros(self.state_space)
            
            # Ensure base_state is properly initialized
            if base_state is None:
                self.logger.warning("base_state is None after processing, using fallback")
                expected_base_size = self.state_space - (self.stock_dim * 3)  # Subtract asymmetric features
                base_state = np.zeros(max(1, expected_base_size), dtype=np.float32)
            
            # Update price history with current prices
            self._update_price_history()
            
            # Get asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()
            self.logger.debug(f"DEBUG: asymmetric_features type: {type(asymmetric_features)}, shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype}")
            if asymmetric_features.size < 10:
                 self.logger.debug(f"DEBUG: asymmetric_features content: {asymmetric_features}")
            
            # CRITICAL FIX: Ensure the enhanced state matches the expected state_space dimension
            # The issue is that base_state might be smaller than expected, so we need to pad it
            expected_base_size = self.state_space - (self.stock_dim * 3)  # Expected base state size
            asymmetric_size = self.stock_dim * 3  # Asymmetric features size
            
            self.logger.debug(f"DEBUG: Expected dimensions - base_size: {expected_base_size}, asymmetric_size: {asymmetric_size}, total: {self.state_space}")
            self.logger.debug(f"DEBUG: Actual dimensions - base_state: {base_state.shape[0]}, asymmetric_features: {asymmetric_features.shape[0]}")
            
            # Pad or truncate base_state to match expected size
            if base_state.shape[0] < expected_base_size:
                # Pad with zeros
                padding_size = expected_base_size - base_state.shape[0]
                base_state = np.concatenate([base_state, np.zeros(padding_size, dtype=np.float32)])
                self.logger.debug(f"DEBUG: Padded base_state with {padding_size} zeros to reach expected size {expected_base_size}")
            elif base_state.shape[0] > expected_base_size:
                # Truncate to expected size
                base_state = base_state[:expected_base_size]
                self.logger.debug(f"DEBUG: Truncated base_state to expected size {expected_base_size}")
            
            # Ensure asymmetric features have the correct size
            if asymmetric_features.shape[0] < asymmetric_size:
                # Pad with zeros
                padding_size = asymmetric_size - asymmetric_features.shape[0]
                asymmetric_features = np.concatenate([asymmetric_features, np.zeros(padding_size, dtype=np.float32)])
                self.logger.debug(f"DEBUG: Padded asymmetric_features with {padding_size} zeros to reach expected size {asymmetric_size}")
            elif asymmetric_features.shape[0] > asymmetric_size:
                # Truncate to expected size
                asymmetric_features = asymmetric_features[:asymmetric_size]
                self.logger.debug(f"DEBUG: Truncated asymmetric_features to expected size {asymmetric_size}")
            
            # Combine base state with asymmetric features
            self.logger.debug(f"DEBUG: Final concatenation with base_state (shape: {base_state.shape}, dtype: {base_state.dtype}) and asymmetric_features (shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype})")

            # FIX: Ensure both arrays are 1D before concatenation
            try:
                if base_state.ndim > 1:
                    base_state = base_state.flatten()
                if asymmetric_features.ndim > 1:
                    asymmetric_features = asymmetric_features.flatten()

                # Ensure both arrays have compatible dtypes and prevent overflow
                # CRITICAL FIX: Clip extreme values before float32 conversion to prevent overflow
                base_state = np.clip(base_state, -1e6, 1e6)  # Reasonable bounds for float32
                base_state = base_state.astype(np.float32)
                asymmetric_features = asymmetric_features.astype(np.float32)
                
                # CRITICAL FIX: Clean arrays before concatenation
                base_state = np.nan_to_num(base_state, nan=0.0, posinf=100.0, neginf=-100.0)
                base_state = np.clip(base_state, -100.0, 100.0)
                
                asymmetric_features = np.nan_to_num(asymmetric_features, nan=0.0, posinf=100.0, neginf=-100.0)
                asymmetric_features = np.clip(asymmetric_features, -100.0, 100.0)

                enhanced_state = np.concatenate([base_state, asymmetric_features])
                
                # Final validation
                if enhanced_state.shape[0] != self.state_space:
                    self.logger.warning(f"DEBUG: Enhanced state size mismatch! Got {enhanced_state.shape[0]}, expected {self.state_space}. Fixing...")
                    if enhanced_state.shape[0] < self.state_space:
                        # Pad with zeros
                        padding_size = self.state_space - enhanced_state.shape[0]
                        enhanced_state = np.concatenate([enhanced_state, np.zeros(padding_size, dtype=np.float32)])
                    else:
                        # Truncate
                        enhanced_state = enhanced_state[:self.state_space]
                        
            except (ValueError, TypeError) as e:
                self.logger.error(f"DEBUG: Failed to concatenate arrays: {e}. Using fallback.")
                # Fallback: create a safe state array
                enhanced_state = np.zeros(self.state_space, dtype=np.float32)
                # Fill with base_state values if possible
                if base_state.size > 0:
                    copy_size = min(base_state.size, self.state_space)
                    enhanced_state[:copy_size] = base_state.flatten()[:copy_size]

            # CRITICAL FIX: Final NaN/inf check on the enhanced state
            if np.any(np.isnan(enhanced_state)) or np.any(np.isinf(enhanced_state)):
                self.logger.error(f"NaN/inf values detected in enhanced_state: {enhanced_state}")
                nan_mask = np.isnan(enhanced_state) | np.isinf(enhanced_state)
                enhanced_state[nan_mask] = 0.0
                self.logger.warning(f"Replaced {np.sum(nan_mask)} NaN/inf values in enhanced_state with 0.0")
            
            self.logger.debug(f"DEBUG: enhanced_state type: {type(enhanced_state)}, shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}, expected state_space: {self.state_space}")
            if enhanced_state.size < 30:
                 self.logger.debug(f"DEBUG: enhanced_state content: {enhanced_state}")

            # CRITICAL FIX: Add comprehensive NaN/Inf protection for SAC training
            # Prevent gradient explosion by ensuring clean state values
            if np.isnan(enhanced_state).any() or np.isinf(enhanced_state).any():
                self.logger.warning("State contains NaN/Inf values - cleaning up aggressively")
                enhanced_state = np.nan_to_num(enhanced_state, nan=0.0, posinf=100.0, neginf=-100.0)
            
            # CRITICAL FIX: Clip extreme values to prevent neural network saturation
            # Use much smaller bounds to prevent instability
            enhanced_state = np.clip(enhanced_state, -100.0, 100.0)

            return enhanced_state
            
        except Exception as e:
            self.logger.error(f"Error in _get_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return safe fallback
            return np.zeros(self.state_space)
    
    def _execute_correct_trading_step(self, actions):
        """Execute trading step with correct portfolio calculation logic.
        
        This method fixes the FinRL bug where holdings store prices instead of shares.
        
        Args:
            actions: Trading actions array
            
        Returns:
            Tuple of (state, reward, terminal, truncated, info)
        """
        try:
            # Get current state components BEFORE moving to next day
            current_cash = self.state[0]
            current_holdings = self.state[1:self.stock_dim+1].copy()  # Number of shares
            
            # Move to next day AFTER getting current state
            self.day += 1
            # CRITICAL FIX: Use correct termination condition based on unique trading days
            terminal = self.day >= self.unique_trading_days - 1
            truncated = False
            
            if terminal:
                # End of data
                info = {'total_asset': self.state[0] + np.sum(self.state[1:self.stock_dim+1] * self.state[self.stock_dim+1:2*self.stock_dim+1])}
                return self.state, 0.0, terminal, truncated, info
            
            # Get actual stock prices for NEW current day (after increment)
            day_data = None
            try:
                # Check if index is DatetimeIndex (after FinRL transformation) or integer day index
                if isinstance(self.df.index, pd.DatetimeIndex):
                    # FinRL converted back to DatetimeIndex, use iloc instead
                    if self.day < len(self.df.index.unique()):
                        unique_dates = sorted(self.df.index.unique())
                        target_date = unique_dates[self.day]
                        day_data = self.df.loc[target_date]
                    else:
                        raise IndexError(f"Day {self.day} exceeds available data")
                else:
                    # Integer day index
                    day_data = self.df.loc[self.day]
                
                if isinstance(day_data, pd.DataFrame):
                    # Multiple stocks - get prices in consistent order
                    day_data = day_data.sort_values('tic') if 'tic' in day_data.columns else day_data
                    current_prices = day_data['close'].values[:self.stock_dim]
                else:
                    # Single stock
                    current_prices = np.array([day_data['close']] * self.stock_dim)
            except Exception as e:
                self.logger.warning(f"Could not get prices for day {self.day}: {type(e).__name__}: {e}. Using previous prices.")
                self.logger.debug(f"DataFrame index type: {type(self.df.index)}, unique values: {self.df.index.unique()[:10]}")
                current_prices = self.state[self.stock_dim+1:2*self.stock_dim+1].copy()
                # Set day_data to previous day's data or create fallback
                try:
                    if isinstance(self.df.index, pd.DatetimeIndex) and self.day > 0:
                        unique_dates = sorted(self.df.index.unique())
                        if self.day - 1 < len(unique_dates):
                            prev_date = unique_dates[self.day - 1]
                            day_data = self.df.loc[prev_date]
                        else:
                            day_data = self.df.iloc[-10:]  # Last 10 rows as fallback
                    elif self.day > 0:
                        day_data = self.df.loc[self.day - 1]
                    else:
                        day_data = self.df.iloc[0]
                except:
                    day_data = self.data if hasattr(self, 'data') else None
            
            # Ensure we have enough prices
            if len(current_prices) < self.stock_dim:
                current_prices = np.pad(current_prices, (0, self.stock_dim - len(current_prices)), 'edge')
            
            new_cash = current_cash
            new_holdings = current_holdings.copy()
            
            # CRITICAL FIX: Aggressive action clipping to prevent portfolio explosion
            max_action = np.max(np.abs(actions))

            # First, clip to reasonable range
            actions = np.clip(actions, -1.0, 1.0)

            # Additional safety: if portfolio is already large, reduce action magnitude
            current_portfolio = new_cash + np.sum(new_holdings * current_prices)
            if current_portfolio > 500000:  # If portfolio > $500K, be more conservative
                action_scale = min(0.5, 100000 / current_portfolio)  # Scale down actions
                actions = actions * action_scale
                if max_action > 1.0:  # Only log if there were originally large actions
                    self.logger.warning(f"Large portfolio detected (${current_portfolio:.0f}), scaling actions by {action_scale:.3f}")

            if max_action > 1.0:  # Log if we had to clip
                self.logger.error(f"LARGE ACTION VALUES DETECTED:")
                self.logger.error(f"  Original max action magnitude: {max_action}")
                self.logger.error(f"  Current cash: ${new_cash:.2e}")
                self.logger.error(f"  Current portfolio: ${current_portfolio:.2e}")
                self.logger.error(f"  Final actions: {actions}")
            
            # Process each action
            trades_executed = []
            for i in range(min(len(actions), self.stock_dim)):
                action = actions[i]
                stock_price = current_prices[i]
                initial_cash = new_cash
                initial_holdings = new_holdings[i]
                
                # DEBUG: Log action processing details for first few problematic cases
                if max_action > 1.0 and i < 3:
                    self.logger.error(f"  Stock {i}: action={action}, price=${stock_price}, cash=${new_cash:.2e}")
                
                if action > 0:  # Buy signal
                    # Calculate how many shares to buy based on action strength and available cash
                    # CRITICAL FIX: Prevent portfolio explosion by capping cash usage more aggressively
                    max_cash_per_trade = min(new_cash * 0.05, 10000)  # Max $10K per trade or 5% of cash (more conservative)
                    action_strength = min(abs(action), 1.0)  # Ensure action is capped at 1.0
                    cash_to_spend = min(new_cash * action_strength * 0.05, max_cash_per_trade)  # More conservative multiplier

                    # Additional safety: ensure we don't spend more than 20% of total cash in one step
                    if cash_to_spend > new_cash * 0.2:
                        cash_to_spend = new_cash * 0.2

                    if cash_to_spend > stock_price and cash_to_spend > 0:  # Can afford at least 1 share
                        shares_to_buy = cash_to_spend / stock_price

                        # CRITICAL FIX: Cap shares to reasonable amounts
                        max_shares_per_trade = min(1000, cash_to_spend / stock_price)  # Max 1000 shares per trade
                        shares_to_buy = min(shares_to_buy, max_shares_per_trade)

                        cost = shares_to_buy * stock_price * (1 + self.buy_cost_pct[i])  # Include transaction costs

                        # Final safety check
                        if cost <= new_cash and cost > 0 and shares_to_buy > 0:
                            new_holdings[i] += shares_to_buy
                            new_cash -= cost
                            trades_executed.append(f"BUY Stock{i}: {shares_to_buy:.2f} shares @ ${stock_price:.2f} = ${cost:.2f}")
                        else:
                            if max_action > 1.0 and i < 3:  # Debug problematic cases
                                self.logger.error(f"  Stock {i}: BUY rejected - cost=${cost:.2f}, new_cash=${new_cash:.2f}, shares={shares_to_buy:.2f}")
                            
                elif action < 0:  # Sell signal
                    # Sell shares based on action strength
                    if new_holdings[i] > 0:
                        shares_to_sell = min(new_holdings[i], new_holdings[i] * abs(action))
                        revenue = shares_to_sell * stock_price * (1 - self.sell_cost_pct[i])  # Include transaction costs
                        
                        new_holdings[i] -= shares_to_sell
                        new_cash += revenue
                        trades_executed.append(f"SELL Stock{i}: {shares_to_sell:.2f} shares @ ${stock_price:.2f} = ${revenue:.2f}")
            
            # Log trades if any were executed
            # if trades_executed:
            #     self.logger.info(f"Day {self.day} TRADES: {'; '.join(trades_executed)}")
            
            # Update state with new values
            new_state = np.zeros_like(self.state)
            new_state[0] = new_cash
            new_state[1:self.stock_dim+1] = new_holdings
            new_state[self.stock_dim+1:2*self.stock_dim+1] = current_prices  # Use real prices, not normalized
            
            # CRITICAL FIX: Ensure no NaN/Inf values in basic state components
            if np.any(np.isnan(new_state[:2*self.stock_dim+1])) or np.any(np.isinf(new_state[:2*self.stock_dim+1])):
                self.logger.warning(f"NaN/Inf detected in basic state components (cash/holdings/prices)")
                new_state[:2*self.stock_dim+1] = np.nan_to_num(new_state[:2*self.stock_dim+1], nan=0.0, posinf=100.0, neginf=-100.0)
                new_state[:2*self.stock_dim+1] = np.clip(new_state[:2*self.stock_dim+1], -100.0, 100.0)
            
            # Copy technical indicators from current day data
            try:
                tech_start_idx = 1 + 2 * self.stock_dim
                if hasattr(self, 'tech_indicator_list') and self.tech_indicator_list:
                    for j, tech in enumerate(self.tech_indicator_list):
                        if tech_start_idx + j < len(new_state):
                            try:
                                if isinstance(day_data, pd.DataFrame) and tech in day_data.columns:
                                    value = day_data[tech].iloc[0]
                                elif isinstance(day_data, pd.Series) and tech in day_data:
                                    value = day_data[tech]
                                else:
                                    value = 0.0
                                
                                # CRITICAL FIX: Ensure no NaN/Inf values in technical indicators
                                if np.isnan(value) or np.isinf(value):
                                    self.logger.debug(f"NaN/Inf detected in tech indicator {tech}: {value}, using 0.0")
                                    value = 0.0
                                
                                new_state[tech_start_idx + j] = float(value)
                            except:
                                new_state[tech_start_idx + j] = 0.0
            except Exception as e:
                self.logger.warning(f"Could not update technical indicators: {e}")
            
            # CRITICAL FIX: Final state cleaning before assignment
            if np.any(np.isnan(new_state)) or np.any(np.isinf(new_state)):
                self.logger.warning(f"NaN/Inf detected in complete new_state, cleaning...")
                new_state = np.nan_to_num(new_state, nan=0.0, posinf=100.0, neginf=-100.0)
                new_state = np.clip(new_state, -100.0, 100.0)
            
            # Update environment state
            self.state = new_state
            self.data = day_data.iloc[0] if isinstance(day_data, pd.DataFrame) else day_data
            
            # Calculate portfolio value and reward
            holdings_value = np.sum(new_holdings * current_prices)
            portfolio_value = new_cash + holdings_value
            
            # DEBUG: Check for explosive values
            if portfolio_value > 1e10:  # More than 10B - clearly broken
                self.logger.error(f"PORTFOLIO EXPLOSION DEBUG:")
                self.logger.error(f"  new_cash: {new_cash}")
                self.logger.error(f"  new_holdings: {new_holdings}")
                self.logger.error(f"  current_prices: {current_prices}")
                self.logger.error(f"  holdings_value: {holdings_value}")
                self.logger.error(f"  portfolio_value: {portfolio_value}")
                
                # Emergency: Reset to reasonable values
                if new_cash > 1e8:
                    new_cash = self.initial_amount
                if np.any(new_holdings > 1e6):  # More than 1M shares - impossible
                    new_holdings = np.clip(new_holdings, 0, 1000)  # Max 1000 shares per stock
                
                # Recalculate with sane values
                holdings_value = np.sum(new_holdings * current_prices)
                portfolio_value = new_cash + holdings_value
                self.logger.error(f"  AFTER EMERGENCY FIX - portfolio_value: {portfolio_value}")
            
            # CRITICAL FIX: Ensure portfolio_value is finite
            if not np.isfinite(portfolio_value):
                self.logger.warning(f"Non-finite portfolio_value: {portfolio_value}, using previous value")
                portfolio_value = getattr(self, '_last_portfolio_value', self.initial_amount)
            self._last_portfolio_value = portfolio_value
            
            # CRITICAL FIX: Get previous portfolio value from asset_memory (previous day's total)
            # This captures both trading effects AND price movements
            if not hasattr(self, 'asset_memory'):
                self.asset_memory = [self.initial_amount]
                previous_portfolio_value = self.initial_amount
            else:
                previous_portfolio_value = self.asset_memory[-1] if self.asset_memory else self.initial_amount
            
            # CRITICAL FIX: Ensure previous_portfolio_value is finite and positive
            if not np.isfinite(previous_portfolio_value) or previous_portfolio_value <= 0:
                self.logger.warning(f"Invalid previous_portfolio_value: {previous_portfolio_value}, using initial_amount")
                previous_portfolio_value = self.initial_amount
            
            # Calculate step reward as daily return
            portfolio_return = (portfolio_value - previous_portfolio_value) / previous_portfolio_value if previous_portfolio_value > 0 else 0.0
            
            # CRITICAL FIX: Ensure reward is finite
            if not np.isfinite(portfolio_return):
                self.logger.warning(f"Non-finite portfolio_return: {portfolio_return}, using 0.0")
                portfolio_return = 0.0
                
            reward = portfolio_return * self.reward_scaling
            
            # CRITICAL FIX: Final reward validation
            if not np.isfinite(reward):
                self.logger.warning(f"Non-finite reward: {reward}, using 0.0")
                reward = 0.0
            
            # CRITICAL FIX: Apply centralized evaluation noise if enabled
            from utils.evaluation_noise import EvaluationNoiseManager
            reward = EvaluationNoiseManager.get_instance().add_reward_noise(reward)
            
            # Update asset memory
            self.asset_memory.append(portfolio_value)
            
            # CRITICAL FIX: Set cumulative_returns for ElegantRL evaluator
            # This provides the mathematically correct episode return instead of summing step rewards
            # IMPORTANT: Apply reward_scaling to match what ElegantRL expects
            unscaled_return = (portfolio_value - self.initial_amount) / self.initial_amount
            
            # CRITICAL FIX: Ensure unscaled_return is finite
            if not np.isfinite(unscaled_return):
                self.logger.warning(f"Non-finite unscaled_return: {unscaled_return}, using 0.0")
                unscaled_return = 0.0
            
            # EMERGENCY FIX: Cap portfolio value to prevent numerical explosion
            # Portfolio values are exploding to 1e+52 (impossible values)
            max_reasonable_portfolio = 1000000  # Hard cap at $1M regardless of initial amount
            if portfolio_value > max_reasonable_portfolio:
                # Only log first few explosions to avoid spam
                if not hasattr(self, '_explosion_count'):
                    self._explosion_count = 0
                self._explosion_count += 1
                if self._explosion_count <= 3:
                    self.logger.warning(f"Portfolio exploded to {portfolio_value}, capping at {max_reasonable_portfolio} (showing first 3 only)")

                # CRITICAL FIX: When portfolio explodes, also fix the underlying state components
                # The explosion is likely due to corrupted cash or holdings values
                self.logger.error(f"PORTFOLIO EXPLOSION - Debugging state components:")
                self.logger.error(f"  Original new_cash: {new_cash}")
                self.logger.error(f"  Original new_holdings: {new_holdings}")
                self.logger.error(f"  Current prices: {current_prices}")

                # Reset to safe values
                if new_cash > max_reasonable_portfolio:
                    new_cash = min(new_cash, max_reasonable_portfolio * 0.5)  # Keep some cash
                    self.logger.error(f"  Capped new_cash to: {new_cash}")

                # Cap holdings to reasonable values (max 10,000 shares per stock)
                max_shares_per_stock = 10000
                if np.any(new_holdings > max_shares_per_stock):
                    new_holdings = np.clip(new_holdings, 0, max_shares_per_stock)
                    self.logger.error(f"  Capped new_holdings to: {new_holdings}")

                # Recalculate portfolio with capped values
                holdings_value = np.sum(new_holdings * current_prices)
                portfolio_value = new_cash + holdings_value

                # Final safety check
                if portfolio_value > max_reasonable_portfolio:
                    # Proportionally reduce both cash and holdings
                    scale_factor = max_reasonable_portfolio / portfolio_value
                    new_cash *= scale_factor
                    new_holdings *= scale_factor
                    portfolio_value = max_reasonable_portfolio
                    self.logger.error(f"  Applied scale factor {scale_factor:.4f} to reach final portfolio: {portfolio_value}")

                # Update the state with corrected values
                self.state[0] = new_cash
                self.state[1:self.stock_dim+1] = new_holdings

                # Update asset memory with capped value
                self.asset_memory[-1] = portfolio_value
            
            # Recalculate return with capped portfolio value
            unscaled_return = (portfolio_value - self.initial_amount) / self.initial_amount
            
            # Additional safety: Cap returns to reasonable levels
            max_reasonable_return = 10.0  # 1000% maximum return
            unscaled_return = np.clip(unscaled_return, -0.8, max_reasonable_return)  # -80% to +1000%
            
            scaled_return = unscaled_return * self.reward_scaling
            
            # CRITICAL FIX: Ensure scaled_return is finite
            if not np.isfinite(scaled_return):
                self.logger.warning(f"Non-finite scaled_return: {scaled_return}, using 0.0")
                scaled_return = 0.0
            
            # Note: Evaluation noise is now handled centrally by EvaluationNoiseManager (line 1386)
            # No additional noise needed here to avoid double-application
            
            # CRITICAL FIX: Set cumulative_returns to actual portfolio return percentage for ElegantRL display
            # ElegantRL shows this as avgR, so use unscaled return (5% = 0.05) not tiny scaled return (0.000005)
            # This makes avgR show meaningful numbers like training does
            self.cumulative_returns = unscaled_return  # Use portfolio return %, not scaled reward
            
            # Prepare info dict
            info = {
                'total_asset': portfolio_value,
                'portfolio_value': portfolio_value,
                'cash': new_cash,
                'holdings': new_holdings.copy(),
                'prices': current_prices.copy()
            }
            
            return new_state, reward, terminal, truncated, info
            
        except Exception as e:
            self.logger.error(f"Error in _execute_correct_trading_step: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return safe defaults
            info = {'total_asset': getattr(self, 'initial_amount', 100000)}
            return self.state if hasattr(self, 'state') else np.zeros(self.observation_space.shape), 0.0, True, False, info

    def step(self, actions):
        """Execute one step in the environment with asymmetric reward calculation.

        Args:
            actions: Trading actions

        Returns:
            Tuple of (state, reward, terminal, truncated, info)
        """
        try:
            # Convert actions to numpy array if needed
            if not isinstance(actions, np.ndarray):
                actions = np.array(actions)

            current_day = getattr(self, 'day', 'unknown')
            self.logger.debug(f"STEP {current_day} START - Actions: {actions}")

            portfolio_before_step = 0.0
            cash_before_step = 0.0
            holdings_before_step = np.zeros(self.stock_dim)
            prices_before_step = np.zeros(self.stock_dim)

            try:
                if hasattr(self, 'state') and self.state is not None and len(self.state) > 2*self.stock_dim:
                    cash_before_step = self.state[0]
                    holdings_before_step = self.state[1:self.stock_dim+1]
                    prices_before_step = self.state[self.stock_dim+1:2*self.stock_dim+1]
                    portfolio_before_step = cash_before_step + np.sum(holdings_before_step * prices_before_step)
                    # self.logger.debug(f"STEP {current_day}: BEFORE - Portfolio: ${portfolio_before_step:.2f}, Cash: ${cash_before_step:.2f}, Holdings: {holdings_before_step}")
                else:
                    self.logger.warning(f"STEP {current_day}: BEFORE - Could not reliably get portfolio state before super().step(). State: {getattr(self, 'state', 'N/A')}")
            except Exception as e:
                self.logger.warning(f"STEP {current_day}: BEFORE - Error logging portfolio state: {e}")

            # CRITICAL FIX: Override buggy FinRL step logic
            # The parent StockTradingEnv has a bug where it stores prices in holdings instead of shares
            state, base_reward, terminal, truncated, info = self._execute_correct_trading_step(actions)
            base_reward = float(base_reward) # Ensure it's a float

            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Base Reward: {base_reward:.8f}, Terminal: {terminal}, Truncated: {truncated}")
            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent (raw): {info}")
            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent (type): {type(info)}")
            if isinstance(info, dict):
                for key, value in info.items():
                    try:
                        value_str = str(value)
                        # Truncate if very long, especially for arrays/dataframes
                        if len(value_str) > 200 and hasattr(value, 'shape'): # Heuristic for large array-like
                            value_preview = f"Object of type {type(value)} with shape {getattr(value, 'shape', 'N/A')}"
                        elif len(value_str) > 200:
                            value_preview = value_str[:197] + "..."
                        else:
                            value_preview = value_str
                        self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info item: key='{key}', type='{type(value)}', value_preview='{value_preview}'")
                    except Exception as e_log_info:
                        self.logger.warning(f"STEP {current_day}: SUPER().STEP - Error logging info item '{key}': {e_log_info}")
            elif info is not None:
                self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent is not a dict, actual value: {str(info)[:200]}")


            portfolio_after_step = 0.0
            cash_after_step = 0.0
            holdings_after_step = np.zeros(self.stock_dim)
            prices_after_step = np.zeros(self.stock_dim)
            try:
                if state is not None and len(state) > 2*self.stock_dim:
                    cash_after_step = state[0]
                    holdings_after_step = state[1:self.stock_dim+1]
                    prices_after_step = state[self.stock_dim+1:2*self.stock_dim+1]
                    portfolio_after_step = cash_after_step + np.sum(holdings_after_step * prices_after_step)
                    portfolio_change = portfolio_after_step - portfolio_before_step if portfolio_before_step else 0.0
                    # self.logger.debug(f"STEP {current_day}: AFTER - Portfolio: ${portfolio_after_step:.2f}, Cash: ${cash_after_step:.2f}, Holdings: {holdings_after_step}, Change: ${portfolio_change:.2f}")
                else:
                    self.logger.warning(f"STEP {current_day}: AFTER - State is None or too short after super().step(). State: {state}")

            except Exception as e:
                self.logger.warning(f"STEP {current_day}: AFTER - Error logging portfolio state: {e}")

            if state is None:
                self.logger.error(f"STEP {current_day}: State is None after parent step. Returning safe defaults.")
                # Attempt to get a valid state, otherwise use zeros
                safe_state = self._get_state() if hasattr(self, '_get_state') else np.zeros(self.observation_space.shape)
                return safe_state, 0.0, True, False, {}

            # Ensure info is a dict
            if not isinstance(info, dict):
                info = {}

            # Add portfolio value to info dict for backtesting
            try:
                current_total_asset = 0.0
                if hasattr(self, 'asset_memory') and self.asset_memory and len(self.asset_memory) > 0:
                    current_total_asset = float(self.asset_memory[-1])
                elif portfolio_after_step > 0: # Use calculated portfolio_after_step if asset_memory is not up-to-date
                    current_total_asset = portfolio_after_step
                
                info['total_asset'] = current_total_asset
                self.logger.debug(f"STEP {current_day}: INFO - total_asset set to: {info['total_asset']:.2f}")

                # Ensure asset_memory is updated if it was used to derive total_asset from portfolio_after_step
                if not (hasattr(self, 'asset_memory') and self.asset_memory and len(self.asset_memory) > 0) and portfolio_after_step > 0:
                    if not hasattr(self, 'asset_memory') or not self.asset_memory:
                        self.asset_memory = [self.initial_amount]
                    self.asset_memory.append(portfolio_after_step)
                    self.logger.debug(f"STEP {current_day}: INFO - asset_memory updated with: {portfolio_after_step:.2f}")

            except Exception as e:
                self.logger.warning(f"STEP {current_day}: INFO - Could not add/verify portfolio value in info: {e}")

            # Get enhanced state with asymmetric features
            enhanced_state = self._get_state()
            if enhanced_state is None:
                self.logger.error(f"STEP {current_day}: enhanced_state is None from _get_state. Returning safe defaults.")
                safe_state = np.zeros(self.observation_space.shape)
                return safe_state, 0.0, True, False, info # return info as is
            
            self.logger.debug(f"STEP {current_day}: Enhanced state shape: {enhanced_state.shape if enhanced_state is not None else 'None'}")

            # Apply asymmetric reward shaping
            market_trend = self._detect_market_trend()
            shaped_reward = self._apply_asymmetric_reward_shaping(base_reward)
            self.logger.debug(f"STEP {current_day}: REWARD SHAPING - Market Trend: {market_trend}, Base Reward: {base_reward:.8f}, Shaped Reward: {shaped_reward:.8f}")

            self.logger.debug(f"STEP {current_day} END - Returning: Shaped Reward: {shaped_reward:.8f}, Terminal: {terminal}, Truncated: {truncated}, Info: {info}")
            return enhanced_state, shaped_reward, terminal, truncated, info

        except Exception as e:
            current_day_except = getattr(self, 'day', 'unknown_in_exception')
            self.logger.error(f"STEP {current_day_except}: CRITICAL ERROR in step method: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return safe defaults
            safe_state_except = self._get_state() if hasattr(self, '_get_state') else np.zeros(self.observation_space.shape if hasattr(self, 'observation_space') and self.observation_space is not None else (10,)) # A small default shape if obs space unknown
            return safe_state_except, 0.0, True, False, {}

    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping based on market trend.

        Args:
            base_reward: Base reward from environment (e.g., portfolio change)

        Returns:
            Asymmetric-shaped reward
        """
        try:
            market_trend = self._detect_market_trend()
            shaped_reward = base_reward  # Default to base reward

            if market_trend == 'down':
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly

            return shaped_reward

        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}. Returning base_reward.")
            return base_reward

    def _detect_market_trend(self) -> str:
        """Detect current market trend based on recent portfolio performance.

        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                # Asset memory stores portfolio value at each step
                # CRITICAL FIX: Clip extreme portfolio values before float32 conversion
                recent_raw = self.asset_memory[-5:]
                recent_clipped = [np.clip(float(val), 0, 1e8) for val in recent_raw]  # Cap at 100M portfolio
                recent_values = np.array(recent_clipped, dtype=np.float32)
                recent_diffs = np.diff(recent_values)

                if len(recent_diffs) == 0:
                    return 'neutral'

                avg_change = np.mean(recent_diffs)

                # Threshold relative to initial capital
                threshold = self.initial_amount * 0.0005  # 0.05% of initial capital

                if avg_change > threshold:
                    return 'up'
                elif avg_change < -threshold:
                    return 'down'
                else:
                    return 'neutral'

            return 'neutral'

        except Exception as e:
            self.logger.warning(f"Error detecting market trend: {e}. Defaulting to 'neutral'.")
            return 'neutral'

    def reset(self, *, seed=None, options=None):
        """Reset the environment.
        
        Args:
            seed: Random seed
            options: Reset options
            
        Returns:
            Tuple of (initial_state, info)
        """
        try:
            # CRITICAL FIX: Add random seed variation for evaluation noise
            # This ensures different episodes even with deterministic actors
            if hasattr(self, '_evaluation_noise_scale') and self._evaluation_noise_scale > 0:
                if seed is None:
                    # Generate a random seed if none provided
                    import time
                    seed = int(time.time() * 1000) % 1000000 + np.random.randint(0, 1000)
                else:
                    # Add random variation to provided seed
                    seed = seed + np.random.randint(0, 1000)
            
            # Reset parent environment
            result = super().reset(seed=seed, options=options)
            # self.logger.debug(f"RESET: SUPER().RESET - Result from parent (raw): {result}")
            self.logger.debug(f"RESET: SUPER().RESET - Result from parent (type): {type(result)}")
            parent_info = {}
            obs_from_parent = None

            if isinstance(result, tuple) and len(result) == 2:
                obs_from_parent, parent_info = result
                self.logger.debug(f"RESET: SUPER().RESET - Obs from parent (type): {type(obs_from_parent)}")
                if hasattr(obs_from_parent, 'shape'):
                    self.logger.debug(f"RESET: SUPER().RESET - Obs from parent (shape): {obs_from_parent.shape}")
                self.logger.debug(f"RESET: SUPER().RESET - Info from parent (raw): {parent_info}")
                self.logger.debug(f"RESET: SUPER().RESET - Info from parent (type): {type(parent_info)}")
                if isinstance(parent_info, dict):
                    for key, value in parent_info.items():
                        try:
                            value_str = str(value)
                            if len(value_str) > 200 and hasattr(value, 'shape'):
                                value_preview = f"Object of type {type(value)} with shape {getattr(value, 'shape', 'N/A')}"
                            elif len(value_str) > 200:
                                value_preview = value_str[:197] + "..."
                            else:
                                value_preview = value_str
                            self.logger.debug(f"RESET: SUPER().RESET - Info item: key='{key}', type='{type(value)}', value_preview='{value_preview}'")
                        except Exception as e_log_info:
                            self.logger.warning(f"RESET: SUPER().RESET - Error logging info item '{key}': {e_log_info}")
                elif parent_info is not None:
                    self.logger.debug(f"RESET: SUPER().RESET - Info from parent is not a dict, actual value: {str(parent_info)[:200]}")
            elif isinstance(result, np.ndarray): # FinRL might just return obs
                obs_from_parent = result
                self.logger.debug(f"RESET: SUPER().RESET - Parent returned only an observation (np.ndarray). Shape: {obs_from_parent.shape}")
                parent_info = {} # No info dict returned by parent
            else:
                 self.logger.warning(f"RESET: SUPER().RESET - Parent returned unexpected result structure: {type(result)}. Full result: {str(result)[:200]}")
                 obs_from_parent = None # Or try to infer if possible
                 parent_info = {}

            # Reset asymmetric-specific state
            self.price_history = {}
            self.volatility_history = {}
            self.momentum_history = {}
            self.asymmetry_scores = {}
            self.previous_portfolio_value = self.initial_amount
            
            # Initialize price history
            self._initialize_price_history()
            
            # Get enhanced initial state with asymmetric features
            enhanced_state = self._get_state()
            
            # Note: State noise is now handled centrally by EvaluationNoiseManager
            # Use centralized manager for consistent noise application across contexts
            from utils.evaluation_noise import EvaluationNoiseManager
            enhanced_state = EvaluationNoiseManager.get_instance().add_state_noise(enhanced_state)
            
            # Return enhanced state with an empty info dict for ElegantRL compatibility
            return enhanced_state, {}
            
        except Exception as e:
            self.logger.error(f"Error in reset: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def get_asymmetric_metrics(self) -> Dict[str, float]:
        """Get asymmetric strategy metrics.
        
        Returns:
            Dictionary of asymmetric metrics
        """
        metrics = {}
        
        try:
            # Calculate overall asymmetry score
            if self.asymmetry_scores:
                metrics['avg_asymmetry_score'] = np.mean(list(self.asymmetry_scores.values()))
                metrics['max_asymmetry_score'] = np.max(list(self.asymmetry_scores.values()))
                metrics['min_asymmetry_score'] = np.min(list(self.asymmetry_scores.values()))
            
            # Calculate average volatility
            if self.volatility_history:
                all_volatilities = []
                for vol_history in self.volatility_history.values():
                    if vol_history:
                        all_volatilities.extend(vol_history)
                if all_volatilities:
                    metrics['avg_volatility'] = np.mean(all_volatilities)
                    metrics['max_volatility'] = np.max(all_volatilities)
            
            # Calculate average momentum
            if self.momentum_history:
                all_momentum = []
                for mom_history in self.momentum_history.values():
                    if mom_history:
                        all_momentum.extend(mom_history)
                if all_momentum:
                    metrics['avg_momentum'] = np.mean(all_momentum)
                    metrics['positive_momentum_ratio'] = np.mean([m > 0 for m in all_momentum])
            
            # Portfolio metrics
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 1:
                returns = np.diff(self.asset_memory) / np.array(self.asset_memory[:-1])
                upside_returns = returns[returns > 0]
                downside_returns = returns[returns < 0]
                
                if len(upside_returns) > 0 and len(downside_returns) > 0:
                    metrics['realized_upside_downside_ratio'] = np.mean(upside_returns) / np.abs(np.mean(downside_returns))
                    metrics['upside_capture_ratio'] = len(upside_returns) / len(returns)
                
        except Exception as e:
            self.logger.warning(f"Error calculating asymmetric metrics: {e}")
        
        return metrics
    
    # Note: Evaluation noise methods removed - use EvaluationNoiseManager.enable()/disable() instead
    # This ensures centralized noise management across all contexts
