2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:15:29 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:15:29 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:17:25 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:17:25 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:_initiate_state:504 | Error in _initiate_state: operands could not be broadcast together with shapes (11,) (10,) 
2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:_initiate_state:505 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 489, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 408, in _initiate_state
    [self.initial_amount]
ValueError: operands could not be broadcast together with shapes (11,) (10,) 

2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:__init__:205 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:__init__:206 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 174, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:37:29 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:37:29 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:43:09 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:43:09 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:43:09 | ERROR    | AsymmetricTradingEnv:_initiate_state:531 | Error in _initiate_state: 'sma_20'
2025-06-09 18:43:09 | ERROR    | AsymmetricTradingEnv:_initiate_state:532 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 516, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:43:09 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:43:09 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:44:53 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:44:53 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:_initiate_state:531 | Error in _initiate_state: 'sma_20'
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:_initiate_state:532 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 516, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:44:53 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:44:53 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:_initiate_state:531 | Error in _initiate_state: 'sma_20'
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:_initiate_state:532 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 516, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:44:53 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:55:44 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:55:44 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:531 | Error in _initiate_state: 'sma_20'
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:532 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 516, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:55:44 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:55:44 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:531 | Error in _initiate_state: 'sma_20'
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:532 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 516, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:55:44 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:56:22 | INFO     | AsymmetricTradingEnv:__init__:196 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 18:56:22 | INFO     | AsymmetricTradingEnv:__init__:197 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:56:22 | ERROR    | AsymmetricTradingEnv:__init__:232 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:56:22 | ERROR    | AsymmetricTradingEnv:__init__:233 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 201, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:59:05 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:59:05 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:_initiate_state:542 | Error in _initiate_state: 'sma_20'
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:_initiate_state:543 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 527, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:__init__:243 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:__init__:244 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 212, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:59:05 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 18:59:05 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:_initiate_state:542 | Error in _initiate_state: 'sma_20'
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:_initiate_state:543 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 527, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:__init__:243 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:59:05 | ERROR    | AsymmetricTradingEnv:__init__:244 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 212, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:00:04 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:00:04 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:00:04 | ERROR    | AsymmetricTradingEnv:__init__:243 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:00:04 | ERROR    | AsymmetricTradingEnv:__init__:244 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 212, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:01:32 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:01:32 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:01:32 | ERROR    | AsymmetricTradingEnv:__init__:243 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:01:32 | ERROR    | AsymmetricTradingEnv:__init__:244 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 212, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:03:27 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:03:27 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:03:27 | ERROR    | AsymmetricTradingEnv:__init__:243 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:03:27 | ERROR    | AsymmetricTradingEnv:__init__:244 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 212, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 1, hmax: 100, initial_amount: 10000
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0], buy_cost_pct: [0.001], sell_cost_pct: [0.001]
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 1 (type: <class 'int'>)
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 10000 (type: <class 'int'>)
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0] (type: <class 'list'>, element types: [<class 'int'>])
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001] (type: <class 'list'>, element types: [<class 'float'>])
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001] (type: <class 'list'>, element types: [<class 'float'>])
2025-06-09 19:07:29 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:07:29 | ERROR    | AsymmetricTradingEnv:_initiate_state:552 | Error in _initiate_state: 'sma_20'
2025-06-09 19:07:29 | ERROR    | AsymmetricTradingEnv:_initiate_state:553 | Traceback: Traceback (most recent call last):
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'sma_20'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 537, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in _initiate_state
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 425, in <genexpr>
    + sum(([self.data[tech]] for tech in self.tech_indicator_list), [])
            ~~~~~~~~~^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'sma_20'

2025-06-09 19:07:29 | ERROR    | AsymmetricTradingEnv:__init__:253 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:07:29 | ERROR    | AsymmetricTradingEnv:__init__:254 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 222, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:270 | State dimension adjustment: parent=76, asymmetric=30, total=106
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:302 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:09:43 | INFO     | AsymmetricTradingEnv:__init__:303 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:270 | State dimension adjustment: parent=77, asymmetric=30, total=107
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:302 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:11:35 | INFO     | AsymmetricTradingEnv:__init__:303 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:12:58 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:12:58 | ERROR    | AsymmetricTradingEnv:__init__:260 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:12:58 | ERROR    | AsymmetricTradingEnv:__init__:261 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 229, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:277 | State dimension adjustment: parent=77, asymmetric=30, total=107
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:309 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:13:54 | INFO     | AsymmetricTradingEnv:__init__:310 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:15:33 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:15:33 | ERROR    | AsymmetricTradingEnv:__init__:260 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:15:33 | ERROR    | AsymmetricTradingEnv:__init__:261 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 229, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:17:11 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:17:11 | ERROR    | AsymmetricTradingEnv:__init__:260 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:17:11 | ERROR    | AsymmetricTradingEnv:__init__:261 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 229, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:18:51 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:18:51 | ERROR    | AsymmetricTradingEnv:__init__:260 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:18:51 | ERROR    | AsymmetricTradingEnv:__init__:261 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 229, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:277 | State dimension adjustment: parent=77, asymmetric=30, total=107
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:309 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:20:57 | INFO     | AsymmetricTradingEnv:__init__:310 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:21:56 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:21:56 | ERROR    | AsymmetricTradingEnv:__init__:260 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:21:56 | ERROR    | AsymmetricTradingEnv:__init__:261 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 229, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:228 | ULTRA DEBUG: self.num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:229 | ULTRA DEBUG: self.buy_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:22:50 | INFO     | AsymmetricTradingEnv:__init__:230 | ULTRA DEBUG: self.sell_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:22:50 | ERROR    | AsymmetricTradingEnv:__init__:265 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:22:50 | ERROR    | AsymmetricTradingEnv:__init__:266 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 234, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:228 | ULTRA DEBUG: self.num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:229 | ULTRA DEBUG: self.buy_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:10 | INFO     | AsymmetricTradingEnv:__init__:230 | ULTRA DEBUG: self.sell_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:10 | ERROR    | AsymmetricTradingEnv:__init__:265 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:23:10 | ERROR    | AsymmetricTradingEnv:__init__:266 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 234, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:228 | ULTRA DEBUG: self.num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:229 | ULTRA DEBUG: self.buy_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:28 | INFO     | AsymmetricTradingEnv:__init__:230 | ULTRA DEBUG: self.sell_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:23:28 | ERROR    | AsymmetricTradingEnv:__init__:265 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:23:28 | ERROR    | AsymmetricTradingEnv:__init__:266 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 234, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:228 | ULTRA DEBUG: self.num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:229 | ULTRA DEBUG: self.buy_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:230 | ULTRA DEBUG: self.sell_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:24:07 | INFO     | AsymmetricTradingEnv:__init__:240 | FINAL DEBUG: About to call parent with converted_num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:24:07 | ERROR    | AsymmetricTradingEnv:__init__:273 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:24:07 | ERROR    | AsymmetricTradingEnv:__init__:274 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 242, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:225 | DEBUG: Assigned converted values to self attributes
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:228 | ULTRA DEBUG: self.num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:229 | ULTRA DEBUG: self.buy_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:230 | ULTRA DEBUG: self.sell_cost_pct = [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:240 | FINAL DEBUG: About to call parent with converted_num_stock_shares = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:290 | State dimension adjustment: parent=77, asymmetric=30, total=107
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:322 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:25:12 | INFO     | AsymmetricTradingEnv:__init__:323 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:227 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:228 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:229 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:281 | State dimension adjustment: parent=77, asymmetric=30, total=107
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:313 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 19:27:24 | INFO     | AsymmetricTradingEnv:__init__:314 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:227 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:228 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:28:22 | INFO     | AsymmetricTradingEnv:__init__:229 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:28:22 | ERROR    | AsymmetricTradingEnv:_initiate_state:563 | Error in _initiate_state: operands could not be broadcast together with shapes (11,) (10,) 
2025-06-09 19:28:22 | ERROR    | AsymmetricTradingEnv:_initiate_state:564 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 548, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 408, in _initiate_state
    [self.initial_amount]
ValueError: operands could not be broadcast together with shapes (11,) (10,) 

2025-06-09 19:28:22 | ERROR    | AsymmetricTradingEnv:__init__:264 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:28:22 | ERROR    | AsymmetricTradingEnv:__init__:265 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 233, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:227 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:228 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:28:44 | INFO     | AsymmetricTradingEnv:__init__:229 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:28:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:563 | Error in _initiate_state: operands could not be broadcast together with shapes (11,) (10,) 
2025-06-09 19:28:44 | ERROR    | AsymmetricTradingEnv:_initiate_state:564 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 548, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 408, in _initiate_state
    [self.initial_amount]
ValueError: operands could not be broadcast together with shapes (11,) (10,) 

2025-06-09 19:28:44 | ERROR    | AsymmetricTradingEnv:__init__:264 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:28:44 | ERROR    | AsymmetricTradingEnv:__init__:265 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 233, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:207 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:208 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:211 | DEBUG: About to call super().__init__() with:
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:212 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:213 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:214 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:215 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:216 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:217 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:218 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:227 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:228 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:30:45 | INFO     | AsymmetricTradingEnv:__init__:229 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64)
2025-06-09 19:30:45 | ERROR    | AsymmetricTradingEnv:_initiate_state:563 | Error in _initiate_state: operands could not be broadcast together with shapes (11,) (10,) 
2025-06-09 19:30:45 | ERROR    | AsymmetricTradingEnv:_initiate_state:564 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 548, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 408, in _initiate_state
    [self.initial_amount]
ValueError: operands could not be broadcast together with shapes (11,) (10,) 

2025-06-09 19:30:45 | ERROR    | AsymmetricTradingEnv:__init__:264 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:30:45 | ERROR    | AsymmetricTradingEnv:__init__:265 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 233, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:37:57 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:37:57 | ERROR    | AsymmetricTradingEnv:_initiate_state:588 | Error in _initiate_state: 'numpy.float64' object has no attribute 'values'
2025-06-09 19:37:57 | ERROR    | AsymmetricTradingEnv:_initiate_state:589 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 573, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 409, in _initiate_state
    + self.data.close.values.tolist()
      ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'numpy.float64' object has no attribute 'values'

2025-06-09 19:37:57 | ERROR    | AsymmetricTradingEnv:__init__:291 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:37:57 | ERROR    | AsymmetricTradingEnv:__init__:292 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 260, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:38:16 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:38:16 | ERROR    | AsymmetricTradingEnv:_initiate_state:588 | Error in _initiate_state: 'numpy.float64' object has no attribute 'values'
2025-06-09 19:38:16 | ERROR    | AsymmetricTradingEnv:_initiate_state:589 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 573, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 409, in _initiate_state
    + self.data.close.values.tolist()
      ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'numpy.float64' object has no attribute 'values'

2025-06-09 19:38:16 | ERROR    | AsymmetricTradingEnv:__init__:291 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:38:16 | ERROR    | AsymmetricTradingEnv:__init__:292 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 260, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:38:56 | INFO     | AsymmetricTradingEnv:__init__:323 | Pre-initialized state with 56 elements
2025-06-09 19:38:56 | ERROR    | AsymmetricTradingEnv:_initiate_state:655 | Error in _initiate_state: 'numpy.float64' object has no attribute 'values'
2025-06-09 19:38:56 | ERROR    | AsymmetricTradingEnv:_initiate_state:656 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 640, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 409, in _initiate_state
    + self.data.close.values.tolist()
      ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'numpy.float64' object has no attribute 'values'

2025-06-09 19:38:56 | ERROR    | AsymmetricTradingEnv:__init__:358 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 19:38:56 | ERROR    | AsymmetricTradingEnv:__init__:359 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 327, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:__init__:323 | Pre-initialized state with 56 elements
2025-06-09 19:39:34 | INFO     | AsymmetricTradingEnv:_initiate_state:706 | Manually constructed base state with 56 elements
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:581 | DEBUG: _get_asymmetric_state_features called
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:586 | DEBUG: Processing 10 tickers: ['AAPL' 'MSFT' 'GOOGL' 'AMZN' 'META' 'NVDA' 'TSLA' 'AVGO' 'ADBE' 'ASML']
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for AAPL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for AAPL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for AAPL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for AAPL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for AAPL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for AAPL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for AAPL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for MSFT
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for MSFT (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for MSFT
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for MSFT (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for MSFT
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for MSFT (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for MSFT: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for GOOGL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for GOOGL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for GOOGL
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for GOOGL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for AMZN
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for AMZN (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for AMZN
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for AMZN (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for AMZN
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for AMZN (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for AMZN: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for META
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for META (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for META
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for META (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for META
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for META (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for META: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for NVDA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for NVDA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for NVDA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for NVDA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for NVDA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for NVDA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for NVDA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for TSLA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for TSLA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for TSLA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for TSLA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for TSLA
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for TSLA (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for TSLA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for AVGO
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for AVGO (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for AVGO
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for AVGO (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for AVGO
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for AVGO (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for AVGO: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for ADBE
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for ADBE (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for ADBE
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for ADBE (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for ADBE
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for ADBE (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for ADBE: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:471 | DEBUG: _calculate_volatility called for ASML
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:474 | DEBUG: _calculate_volatility returning 0.0 for ASML (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:503 | DEBUG: _calculate_momentum called for ASML
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:506 | DEBUG: _calculate_momentum returning 0.0 for ASML (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:535 | DEBUG: _calculate_asymmetry_score called for ASML
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:538 | DEBUG: _calculate_asymmetry_score returning 0.0 for ASML (insufficient data)
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:594 | DEBUG: Features for ASML: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:39:34 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:600 | DEBUG: Final asymmetric features array length: 30
2025-06-09 19:39:34 | ERROR    | AsymmetricTradingEnv:__init__:358 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:39:34 | ERROR    | AsymmetricTradingEnv:__init__:359 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 327, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:__init__:351 | Pre-initialized state with 56 elements
2025-06-09 19:40:00 | INFO     | AsymmetricTradingEnv:_initiate_state:734 | Manually constructed base state with 56 elements
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:609 | DEBUG: _get_asymmetric_state_features called
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:614 | DEBUG: Processing 10 tickers: ['AAPL' 'MSFT' 'GOOGL' 'AMZN' 'META' 'NVDA' 'TSLA' 'AVGO' 'ADBE' 'ASML']
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for AAPL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for AAPL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for AAPL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for AAPL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for MSFT
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for MSFT
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for MSFT
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for MSFT: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for GOOGL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for GOOGL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for GOOGL
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for GOOGL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for AMZN
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for AMZN
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for AMZN
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for AMZN: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for META
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for META (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for META
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for META (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for META
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for META (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for META: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for NVDA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for NVDA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for NVDA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for NVDA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for TSLA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for TSLA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for TSLA
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for TSLA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for AVGO
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for AVGO
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for AVGO
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for AVGO: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for ADBE
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for ADBE
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for ADBE
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for ADBE: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:499 | DEBUG: _calculate_volatility called for ASML
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:502 | DEBUG: _calculate_volatility returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:531 | DEBUG: _calculate_momentum called for ASML
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:534 | DEBUG: _calculate_momentum returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:563 | DEBUG: _calculate_asymmetry_score called for ASML
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:566 | DEBUG: _calculate_asymmetry_score returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:622 | DEBUG: Features for ASML: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:00 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:628 | DEBUG: Final asymmetric features array length: 30
2025-06-09 19:40:00 | ERROR    | AsymmetricTradingEnv:__init__:386 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:40:00 | ERROR    | AsymmetricTradingEnv:__init__:387 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 355, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:26 | WARNING  | AsymmetricTradingEnv:__init__:301 | Could not properly structure data: cannot access local variable 'pd' where it is not associated with a value. Using first row.
2025-06-09 19:40:26 | WARNING  | AsymmetricTradingEnv:__init__:329 | Could not get initial prices from data: cannot access local variable 'pd' where it is not associated with a value. Using default.
2025-06-09 19:40:26 | WARNING  | AsymmetricTradingEnv:__init__:342 | Could not initialize technical indicators: cannot access local variable 'pd' where it is not associated with a value. Using defaults.
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:__init__:347 | Pre-initialized state with 56 elements
2025-06-09 19:40:26 | INFO     | AsymmetricTradingEnv:_initiate_state:730 | Manually constructed base state with 56 elements
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:605 | DEBUG: _get_asymmetric_state_features called
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:610 | DEBUG: Processing 10 tickers: ['AAPL' 'MSFT' 'GOOGL' 'AMZN' 'META' 'NVDA' 'TSLA' 'AVGO' 'ADBE' 'ASML']
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AAPL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AAPL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AAPL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AAPL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for MSFT
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for MSFT
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for MSFT
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for MSFT: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for GOOGL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for GOOGL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for GOOGL
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for GOOGL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AMZN
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AMZN
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AMZN
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AMZN: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for META
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for META (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for META
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for META (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for META
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for META (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for META: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for NVDA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for NVDA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for NVDA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for NVDA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for TSLA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for TSLA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for TSLA
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for TSLA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AVGO
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AVGO
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AVGO
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AVGO: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for ADBE
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for ADBE
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for ADBE
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for ADBE: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for ASML
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for ASML
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for ASML
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for ASML: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:26 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:624 | DEBUG: Final asymmetric features array length: 30
2025-06-09 19:40:26 | ERROR    | AsymmetricTradingEnv:__init__:382 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:40:26 | ERROR    | AsymmetricTradingEnv:__init__:383 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 351, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:40:44 | WARNING  | AsymmetricTradingEnv:__init__:301 | Could not properly structure data: cannot access local variable 'pd' where it is not associated with a value. Using first row.
2025-06-09 19:40:44 | WARNING  | AsymmetricTradingEnv:__init__:329 | Could not get initial prices from data: cannot access local variable 'pd' where it is not associated with a value. Using default.
2025-06-09 19:40:44 | WARNING  | AsymmetricTradingEnv:__init__:342 | Could not initialize technical indicators: cannot access local variable 'pd' where it is not associated with a value. Using defaults.
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:__init__:347 | Pre-initialized state with 56 elements
2025-06-09 19:40:44 | INFO     | AsymmetricTradingEnv:_initiate_state:730 | Manually constructed base state with 56 elements
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:605 | DEBUG: _get_asymmetric_state_features called
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:610 | DEBUG: Processing 10 tickers: ['AAPL' 'MSFT' 'GOOGL' 'AMZN' 'META' 'NVDA' 'TSLA' 'AVGO' 'ADBE' 'ASML']
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AAPL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AAPL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AAPL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AAPL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AAPL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for MSFT
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for MSFT
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for MSFT
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for MSFT (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for MSFT: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for GOOGL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for GOOGL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for GOOGL
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for GOOGL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AMZN
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AMZN
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AMZN
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AMZN (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AMZN: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for META
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for META (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for META
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for META (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for META
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for META (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for META: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for NVDA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for NVDA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for NVDA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for NVDA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for NVDA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for TSLA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for TSLA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for TSLA
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for TSLA (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for TSLA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for AVGO
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for AVGO
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for AVGO
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for AVGO (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for AVGO: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for ADBE
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for ADBE
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for ADBE
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for ADBE (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for ADBE: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:495 | DEBUG: _calculate_volatility called for ASML
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:498 | DEBUG: _calculate_volatility returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:527 | DEBUG: _calculate_momentum called for ASML
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:530 | DEBUG: _calculate_momentum returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:559 | DEBUG: _calculate_asymmetry_score called for ASML
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:562 | DEBUG: _calculate_asymmetry_score returning 0.0 for ASML (insufficient data)
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:618 | DEBUG: Features for ASML: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:40:44 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:624 | DEBUG: Final asymmetric features array length: 30
2025-06-09 19:40:44 | ERROR    | AsymmetricTradingEnv:__init__:382 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:40:44 | ERROR    | AsymmetricTradingEnv:__init__:383 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 351, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 1000000
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 1000000 (type: <class 'int'>)
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 56 elements
2025-06-09 19:41:26 | INFO     | AsymmetricTradingEnv:_initiate_state:729 | Manually constructed base state with 56 elements
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:604 | DEBUG: _get_asymmetric_state_features called
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:609 | DEBUG: Processing 10 tickers: ['AAPL' 'MSFT' 'GOOGL' 'AMZN' 'META' 'NVDA' 'TSLA' 'AVGO' 'ADBE' 'ASML']
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for AAPL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for AAPL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for AAPL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for AAPL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for AAPL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for AAPL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for AAPL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for MSFT
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for MSFT (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for MSFT
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for MSFT (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for MSFT
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for MSFT (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for MSFT: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for GOOGL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for GOOGL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for GOOGL
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for GOOGL (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for GOOGL: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for AMZN
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for AMZN (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for AMZN
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for AMZN (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for AMZN
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for AMZN (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for AMZN: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for META
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for META (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for META
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for META (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for META
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for META (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for META: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for NVDA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for NVDA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for NVDA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for NVDA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for NVDA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for NVDA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for NVDA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for TSLA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for TSLA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for TSLA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for TSLA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for TSLA
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for TSLA (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for TSLA: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for AVGO
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for AVGO (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for AVGO
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for AVGO (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for AVGO
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for AVGO (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for AVGO: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for ADBE
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for ADBE (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for ADBE
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for ADBE (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for ADBE
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for ADBE (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for ADBE: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:494 | DEBUG: _calculate_volatility called for ASML
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_volatility:497 | DEBUG: _calculate_volatility returning 0.0 for ASML (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:526 | DEBUG: _calculate_momentum called for ASML
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_momentum:529 | DEBUG: _calculate_momentum returning 0.0 for ASML (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:558 | DEBUG: _calculate_asymmetry_score called for ASML
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_calculate_asymmetry_score:561 | DEBUG: _calculate_asymmetry_score returning 0.0 for ASML (insufficient data)
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:617 | DEBUG: Features for ASML: vol=0.0, mom=0.0, asym=0.0
2025-06-09 19:41:27 | DEBUG    | AsymmetricTradingEnv:_get_asymmetric_state_features:623 | DEBUG: Final asymmetric features array length: 30
2025-06-09 19:41:27 | ERROR    | AsymmetricTradingEnv:__init__:381 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:41:27 | ERROR    | AsymmetricTradingEnv:__init__:382 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 350, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 19:48:12 | WARNING  | AsymmetricTradingEnv:__init__:341 | Could not initialize technical indicators: could not convert string to float: 'moderate_volatility'. Using defaults.
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 92 elements
2025-06-09 19:48:12 | INFO     | AsymmetricTradingEnv:_initiate_state:729 | Manually constructed base state with 57 elements
2025-06-09 19:48:12 | ERROR    | AsymmetricTradingEnv:__init__:381 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 19:48:12 | ERROR    | AsymmetricTradingEnv:__init__:382 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 350, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:05:26 | WARNING  | AsymmetricTradingEnv:__init__:341 | Could not initialize technical indicators: could not convert string to float: 'moderate_volatility'. Using defaults.
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 92 elements
2025-06-09 20:05:26 | INFO     | AsymmetricTradingEnv:_initiate_state:729 | Manually constructed base state with 57 elements
2025-06-09 20:05:26 | ERROR    | AsymmetricTradingEnv:__init__:381 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:05:26 | ERROR    | AsymmetricTradingEnv:__init__:382 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 350, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 56 elements
2025-06-09 20:22:30 | INFO     | AsymmetricTradingEnv:_initiate_state:729 | Manually constructed base state with 56 elements
2025-06-09 20:22:30 | ERROR    | AsymmetricTradingEnv:__init__:381 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:22:30 | ERROR    | AsymmetricTradingEnv:__init__:382 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 350, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 56 elements
2025-06-09 20:28:52 | INFO     | AsymmetricTradingEnv:_initiate_state:729 | Manually constructed base state with 56 elements
2025-06-09 20:28:52 | ERROR    | AsymmetricTradingEnv:__init__:381 | Failed to initialize parent StockTradingEnv: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:28:52 | ERROR    | AsymmetricTradingEnv:__init__:382 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 350, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 97, in __init__
    self.date_memory = [self._get_date()]
                        ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 486, in _get_date
    date = self.data.date.unique()[0]
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Timestamp' object has no attribute 'unique'

2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 56 elements
2025-06-09 20:42:15 | INFO     | AsymmetricTradingEnv:_initiate_state:775 | Manually constructed base state with 56 elements
2025-06-09 20:42:15 | ERROR    | AsymmetricTradingEnv:reset:1152 | Error in reset: 'AsymmetricTradingEnv' object has no attribute 'episode'
2025-06-09 20:42:15 | ERROR    | AsymmetricTradingEnv:reset:1153 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 1098, in reset
    result = super().reset(seed=seed, options=options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 395, in reset
    self.episode += 1
    ^^^^^^^^^^^^
AttributeError: 'AsymmetricTradingEnv' object has no attribute 'episode'

2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:226 | Parameter validation successful - stock_dim: 10, hmax: 100, initial_amount: 100000
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:227 | num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:230 | DEBUG: About to call super().__init__() with:
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:231 |   stock_dim: 10 (type: <class 'int'>)
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:232 |   hmax: 100 (type: <class 'int'>)
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:233 |   initial_amount: 100000 (type: <class 'int'>)
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:234 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] (type: <class 'list'>, element types: [<class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>, <class 'int'>])
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:235 |   buy_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:236 |   sell_cost_pct: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001] (type: <class 'list'>, element types: [<class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>, <class 'float'>])
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:237 |   reward_scaling: 0.0001 (type: <class 'float'>)
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:254 | NUMPY CONVERSION: converted_num_stock_shares = [0 0 0 0 0 0 0 0 0 0] (type: <class 'numpy.ndarray'>, dtype: int64, shape: (10,))
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:255 | NUMPY CONVERSION: converted_buy_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:256 | NUMPY CONVERSION: converted_sell_cost_pct = [0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001 0.001] (type: <class 'numpy.ndarray'>, dtype: float64, shape: (10,))
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:__init__:346 | Pre-initialized state with 56 elements
2025-06-09 20:47:10 | INFO     | AsymmetricTradingEnv:_initiate_state:781 | Manually constructed base state with 56 elements
2025-06-09 20:47:10 | ERROR    | AsymmetricTradingEnv:reset:1158 | Error in reset: 'AsymmetricTradingEnv' object has no attribute 'episode'
2025-06-09 20:47:10 | ERROR    | AsymmetricTradingEnv:reset:1159 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 1104, in reset
    result = super().reset(seed=seed, options=options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 395, in reset
    self.episode += 1
    ^^^^^^^^^^^^
AttributeError: 'AsymmetricTradingEnv' object has no attribute 'episode'

